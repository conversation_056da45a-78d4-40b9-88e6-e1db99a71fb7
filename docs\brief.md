# Project Brief: 本地卡片画布与AI交互工具

## Executive Summary

**项目概念**: 一个本地运行的web应用，提供可自由编辑的卡片画布，支持Markdown语法，卡片间可连线串联，并能将连接的卡片作为上下文提交给AI智能体进行交互。

**主要问题**: 小程序员团队缺乏轻量级、本地优先的知识管理工具，现有工具要么过于复杂，要么依赖云端，无法满足快速整理思路并与AI交互的需求。

**目标市场**: 2-20人的小程序员团队，需要本地知识管理和AI辅助工作的技术人员。

**核心价值**: 
- 本地数据控制，无隐私担忧
- Markdown标准化，便于导出到其他工具
- 可视化连接，帮助整理复杂思路
- 直接AI集成，提升工作效率

## Problem Statement

**当前状态和痛点**:
小程序员团队在日常工作中面临知识管理的多重挑战：
- 技术文档、想法、代码片段分散在各种工具中（记事本、浏览器书签、聊天记录）
- 现有知识管理工具要么功能过重（Notion、Obsidian），要么缺乏程序员友好的特性
- 团队协作时缺乏快速整理和可视化复杂技术概念的工具
- AI工具使用时需要手动复制粘贴大量上下文，效率低下

**问题影响**:
- **时间成本**: 每天花费30-60分钟在不同工具间切换和整理信息
- **知识丢失**: 重要的技术洞察和解决方案因为没有合适的记录方式而遗失
- **协作效率**: 团队成员难以快速理解和接手他人的思路脉络
- **AI交互障碍**: 无法高效地将结构化知识作为上下文提供给AI助手

**现有解决方案的不足**:
- **Obsidian**: 功能强大但学习曲线陡峭，对小团队过于复杂
- **Notion**: 依赖云端，加载慢，不适合敏感技术信息
- **Miro/Figma**: 专注于设计协作，缺乏代码和技术文档的良好支持
- **传统笔记工具**: 缺乏可视化连接和AI集成能力

**解决紧迫性**:
随着AI工具在开发工作中的普及，能够快速整理和提供结构化上下文的需求越来越迫切。小团队需要一个轻量级、本地优先、程序员友好的解决方案来提升工作效率。

## Proposed Solution

**核心概念和方法**:
开发一个本地运行的web应用，提供直观的卡片画布界面，让用户能够：
- 创建支持Markdown语法的知识卡片
- 通过可视化连线建立卡片间的关系
- 将连接的卡片网络作为结构化上下文一键提交给AI智能体
- 使用多画布系统按主题组织不同的知识领域

**与现有解决方案的关键差异**:
- **本地优先**: 使用SQLite WASM实现纯前端数据存储，无需服务器
- **程序员友好**: 严格遵循Markdown语法，确保与开发工具生态的兼容性
- **轻量级设计**: 专注核心功能，避免功能膨胀，适合小团队快速上手
- **AI原生集成**: 内置OpenRouter API支持，直接将卡片网络转换为AI上下文
- **可视化思维**: 通过连线展示知识间的关系，帮助理解复杂概念

**成功的独特优势**:
- **零学习成本**: 程序员已熟悉Markdown，无需学习新语法
- **数据主权**: 完全本地存储，团队对敏感技术信息有完全控制权
- **高效AI交互**: 消除手动复制粘贴的繁琐，一键生成结构化上下文
- **渐进式复杂度**: 从简单卡片开始，可逐步构建复杂的知识网络
- **导出友好**: 标准Markdown格式确保与其他工具的无缝集成

**产品愿景**:
成为小程序员团队的"数字白板"，在传统笔记工具和复杂知识管理系统之间找到完美平衡点。让技术团队能够像在白板上画图一样自然地整理思路，同时享受数字化工具的便利和AI助手的智能支持。

## Target Users

### Primary User Segment: 小型程序员团队成员

**人群特征**:
- **团队规模**: 2-20人的技术团队（初创公司、小型软件公司、独立开发团队）
- **技术背景**: 熟悉Markdown语法，日常使用Git、IDE等开发工具
- **年龄范围**: 25-40岁，有3-10年编程经验
- **工作环境**: 远程或混合办公，重视工作效率和工具简洁性

**当前行为和工作流程**:
- 使用多种工具记录技术笔记（VS Code、Notion、Google Docs、纸质笔记）
- 频繁在GitHub、Stack Overflow、技术博客间切换查找信息
- 团队沟通主要通过Slack/Discord + 代码review
- 开始使用ChatGPT/Claude等AI工具辅助编程，但上下文管理混乱

**具体需求和痛点**:
- **知识碎片化**: 技术方案、API文档、代码片段散落各处，难以形成体系
- **上下文切换成本**: 在不同工具间频繁切换，打断思维流程
- **团队知识传承**: 缺乏有效方式让新成员快速理解项目技术脉络
- **AI交互效率**: 需要手动整理大量背景信息才能有效使用AI助手

**目标和期望**:
- 快速记录和整理技术想法，不打断编程流程
- 可视化展示复杂技术架构和依赖关系
- 高效利用AI工具解决技术问题
- 与团队成员分享结构化的技术知识

### Secondary User Segment: 技术主管和架构师

**人群特征**:
- **职位**: Tech Lead、架构师、CTO
- **责任**: 技术决策、团队指导、架构设计
- **经验**: 5-15年技术经验，管理2-10人团队

**特殊需求**:
- **技术决策记录**: 需要记录架构选择的原因和权衡
- **团队培训**: 需要创建技术培训材料和知识传承文档
- **项目规划**: 需要可视化展示技术路线图和依赖关系
- **跨项目知识管理**: 管理多个项目的技术知识和最佳实践

## Goals & Success Metrics

### Business Objectives
- **用户采用**: 6个月内获得50个活跃小团队用户（100-500个人用户）
- **产品验证**: 达到70%的用户留存率（30天内），证明产品解决了真实需求
- **技术可行性**: 成功实现SQLite WASM + AI集成的技术架构，为后续扩展奠定基础
- **社区建设**: 建立开源社区，获得100+ GitHub stars，形成用户反馈循环

### User Success Metrics
- **使用频率**: 用户平均每周使用3-5次，每次会话时长15-45分钟
- **内容创建**: 用户平均创建20-50个卡片，建立10-30个连线关系
- **AI交互**: 70%的用户使用AI功能，平均每周进行5-10次AI查询
- **数据导出**: 50%的用户使用导出功能，证明与现有工具生态的集成价值
- **多画布使用**: 活跃用户平均创建2-4个主题画布，证明组织功能的价值

### Key Performance Indicators (KPIs)
- **技术性能**: 
  - 应用启动时间 < 3秒
  - 卡片创建响应时间 < 500ms
  - SQLite查询响应时间 < 100ms
  - AI API调用成功率 > 95%

- **用户体验**:
  - 新用户完成首次卡片创建的时间 < 2分钟
  - 用户从创建卡片到AI交互的完整流程时间 < 5分钟
  - 用户报告的bug数量 < 1个/用户/月

- **产品采用**:
  - 月活跃用户增长率 > 20%
  - 用户推荐率(NPS) > 50
  - 功能使用覆盖率: 核心功能使用率 > 80%

## MVP Scope

### Core Features (Must Have)

- **卡片管理系统**: 点击画布创建卡片，支持实时Markdown编辑与语法高亮，一键删除带撤销功能。卡片根据内容自适应大小，支持手动调整。这是产品的基础功能，没有卡片就没有后续的所有功能。

- **连线功能**: 拖拽卡片边缘到另一个卡片创建连接，支持连线删除和路径调整。连线提供视觉反馈（悬停高亮、选中状态）。这是实现知识关系可视化的核心功能。

- **画布操作**: 无限画布支持缩放、平移操作，确保用户可以自由组织大量卡片。这是用户体验的基础保障。

- **SQLite WASM存储**: 实现本地数据持久化，支持卡片内容、位置、连线关系的可靠存储。这是产品"本地优先"价值主张的技术基础。

- **OpenRouter API集成**: 配置API密钥，选择大模型，将选中的卡片网络作为上下文提交给AI。这是产品的核心差异化功能。

- **多画布支持**: 创建多个主题画布，当前画布内容加载到内存，其他画布按需加载，LRU缓存策略管理内存使用。这是满足用户组织需求的关键功能。

### Out of Scope for MVP
- 实时协作功能
- 云端同步
- 用户账户系统
- 复杂的连线类型（标签、方向性）
- 网格对齐系统
- 卡片模板系统
- 高级导出选项（PDF、图片）
- 主题切换
- 搜索功能
- 分组功能
- 撤销/重做系统

### MVP Success Criteria
MVP被认为成功当且仅当：
- 用户能在5分钟内创建包含5个卡片和3个连线的知识网络
- 数据在浏览器关闭重开后100%保持完整
- AI调用能成功返回基于卡片上下文的有意义回复
- 应用在包含50个卡片的画布上保持流畅操作（<500ms响应时间）
- 用户能成功导出Markdown格式的卡片内容

## Post-MVP Vision

### Phase 2 Features

**增强用户体验**:
- **搜索和索引**: 全文搜索卡片内容，按标签和创建时间过滤，快速定位相关信息
- **撤销/重做系统**: 完整的操作历史管理，支持复杂编辑流程的回滚
- **卡片模板**: 预设常用模板（会议记录、技术方案、问题分析），提升创建效率
- **导出增强**: 支持PDF报告生成、画布截图、完整项目打包导出

**协作功能**:
- **本地文件共享**: 通过文件系统实现简单的团队共享（导出/导入机制）
- **版本控制集成**: 与Git集成，将画布变更纳入版本管理
- **评论系统**: 在卡片上添加评论和标注，支持异步协作

### Long-term Vision

**成为技术团队的"数字大脑"**: 在1-2年内，产品将从简单的卡片工具演进为智能知识助手。通过机器学习分析用户的知识网络模式，主动建议相关连接，识别知识盲点，推荐学习资源。

**AI原生工作流**: 深度集成多种AI能力，不仅限于文本生成，还包括代码分析、架构建议、技术决策支持。AI将成为用户知识网络的智能伙伴，而不仅仅是查询工具。

**生态系统集成**: 与主流开发工具深度集成（VS Code插件、GitHub Actions、Slack机器人），让知识管理无缝融入现有工作流程。

### Expansion Opportunities

**垂直领域扩展**:
- **产品团队版本**: 针对产品经理和设计师的需求定制功能
- **研究团队版本**: 支持学术研究和文献管理的特殊需求
- **企业版本**: 添加权限管理、审计日志、合规性功能

**技术平台扩展**:
- **移动应用**: 开发移动端查看器，支持随时查看和简单编辑
- **桌面应用**: Electron封装，提供更好的本地集成体验
- **浏览器插件**: 快速收集网页内容到卡片系统

**商业模式探索**:
- **开源核心 + 商业插件**: 保持核心功能开源，提供高级功能的商业版本
- **咨询服务**: 为企业提供知识管理流程咨询和定制开发
- **培训课程**: 开发关于高效知识管理和AI协作的在线课程

## Technical Considerations

### Platform Requirements
- **Target Platforms**: 现代Web浏览器（Chrome 90+, Firefox 88+, Safari 14+, Edge 90+）
- **Browser/OS Support**:
  - Windows 10/11, macOS 10.15+, Ubuntu 18.04+
  - 支持WebAssembly和现代JavaScript特性
  - 最低4GB RAM，推荐8GB以获得最佳性能
- **Performance Requirements**:
  - 应用启动时间 < 3秒
  - 卡片操作响应时间 < 500ms
  - 支持单画布100个卡片流畅运行
  - SQLite查询响应时间 < 100ms

### Technology Preferences
- **Frontend**:
  - React 18+ with TypeScript（类型安全和组件化）
  - TailwindCSS（快速样式开发）
  - Zustand（轻量级状态管理）
- **Backend**:
  - 无传统后端，纯前端架构
  - SQLite WASM（本地数据库）
  - Service Worker（缓存和离线支持）
- **Database**:
  - SQLite WASM with sql.js
  - 前端索引优化频繁查询
  - LRU缓存策略管理内存使用
- **Hosting/Infrastructure**:
  - 静态文件托管（GitHub Pages, Netlify, Vercel）
  - CDN加速资源加载
  - 无服务器架构，降低维护成本

### Architecture Considerations
- **Repository Structure**:
  - 单仓库结构，包含前端应用和文档
  - 清晰的组件分层：UI层、业务逻辑层、数据层
  - 独立的工具函数和类型定义
- **Service Architecture**:
  - 模块化设计：卡片管理、画布渲染、AI集成、数据存储
  - 事件驱动架构处理用户交互
  - 插件化AI提供商支持（OpenRouter、直接API调用）
- **Integration Requirements**:
  - OpenRouter API集成（支持多种大模型）
  - Markdown解析和渲染（marked.js + highlight.js）
  - 文件导入/导出（JSON、Markdown格式）
- **Security/Compliance**:
  - 本地数据存储，无隐私泄露风险
  - API密钥本地加密存储
  - 无用户数据收集，符合GDPR要求
  - 开源代码，可审计安全性

## Constraints & Assumptions

### Constraints
- **Budget**:
  - 零预算项目，依赖开源技术栈
  - 无付费服务依赖（托管使用免费层）
  - 开发工具限制在免费/开源选项
- **Timeline**:
  - MVP开发周期：4-6周
  - 单人开发，业余时间投入
  - 每周预计投入15-20小时开发时间
- **Resources**:
  - 单一开发者，全栈技能要求
  - 无专门的设计师、测试人员
  - 依赖社区反馈进行产品迭代
- **Technical**:
  - 浏览器兼容性限制（需要WASM支持）
  - 本地存储容量限制（浏览器存储配额）
  - 无法实现实时协作功能（无后端服务器）
  - AI API调用受限于CORS政策

### Key Assumptions
- **用户技术能力**: 目标用户熟悉Markdown语法，能够理解本地存储的概念和限制
- **市场需求**: 小程序员团队确实存在对轻量级知识管理工具的需求，愿意尝试新工具
- **技术可行性**: SQLite WASM能够在浏览器中稳定运行，性能满足用户需求
- **AI集成价值**: 用户认为将结构化知识作为AI上下文的功能有实际价值，愿意配置API密钥
- **本地优先偏好**: 目标用户群体重视数据隐私和本地控制，愿意接受无云端同步的限制
- **开源社区支持**: 开源发布后能够吸引贡献者和用户反馈，形成良性发展循环
- **工具集成需求**: 用户需要与现有工具（Obsidian、Notion等）的数据互操作性
- **学习成本接受度**: 用户愿意投入少量时间学习新的知识组织方式（卡片+连线）

## Risks & Open Questions

### Key Risks
- **技术风险 - SQLite WASM性能瓶颈**: 在大数据量或复杂查询时，浏览器内SQLite可能出现性能问题或内存溢出，影响用户体验。缓解策略包括数据分页、查询优化和降级到简单存储方案。

- **用户采用风险 - 市场需求验证失败**: 目标用户群体可能对现有工具满意，不愿意尝试新的知识管理方式。需要通过早期用户访谈和MVP快速验证来降低此风险。

- **技术债务风险 - 架构扩展性限制**: 纯前端架构可能在功能扩展时遇到瓶颈，特别是协作功能和高级AI集成。需要在设计时考虑未来的架构演进路径。

- **依赖风险 - 第三方API变更**: OpenRouter API政策变更或服务中断可能影响核心功能。需要设计多提供商支持和降级方案。

- **数据安全风险 - 本地数据丢失**: 浏览器缓存清理或设备故障可能导致用户数据永久丢失。必须提供可靠的备份和恢复机制。

### Open Questions
- 用户是否真的会定期使用卡片连线功能，还是更倾向于简单的列表式组织？
- AI集成的最佳交互模式是什么？一键发送全部上下文还是让用户选择特定卡片？
- 多画布功能是否会让用户感到困惑，还是确实有助于信息组织？
- 本地存储的容量限制在实际使用中会成为问题吗？
- 用户对数据导出格式有什么具体偏好？标准Markdown是否足够？
- 如何平衡功能丰富性和界面简洁性？
- 是否需要提供数据迁移工具来帮助用户从其他工具导入现有内容？

### Areas Needing Further Research
- **竞品深度分析**: 详细研究Obsidian、Logseq、Roam Research的用户反馈，识别未满足的需求
- **用户行为研究**: 通过用户访谈了解小程序员团队的实际知识管理工作流程
- **技术可行性验证**: 构建SQLite WASM性能测试原型，验证在目标数据规模下的表现
- **AI集成最佳实践**: 研究现有AI工具的上下文管理方式，学习最佳交互模式
- **浏览器存储限制**: 深入研究不同浏览器的存储配额政策和限制
- **开源项目运营**: 学习成功开源项目的社区建设和维护策略
- **无障碍性要求**: 研究知识管理工具的无障碍性标准和实现方法

## Appendices

### A. Research Summary

**市场研究要点**:
- **现有工具分析**: Obsidian (复杂但功能强大)、Notion (云端依赖)、Logseq (本地优先但学习曲线陡峭)
- **用户痛点识别**: 工具切换成本高、AI集成不够自然、本地数据控制需求强烈
- **技术趋势**: WASM技术成熟、AI工具普及、本地优先架构兴起

**竞争分析发现**:
- 市场上缺乏专门针对小程序员团队的轻量级解决方案
- 现有工具要么功能过重，要么缺乏AI原生集成
- 本地优先 + AI集成的组合具有差异化优势

**技术可行性研究**:
- SQLite WASM在现代浏览器中表现稳定
- 类似项目（如Logseq）证明了本地优先架构的可行性
- OpenRouter API提供了稳定的AI集成基础

### B. Stakeholder Input

**目标用户反馈** (基于需求挖掘过程):
- 强烈支持本地数据控制和Markdown标准化
- 对AI集成功能表示高度兴趣
- 担心学习成本，希望保持界面简洁
- 重视与现有工具的数据互操作性

**技术社区观点**:
- 开源社区对本地优先工具有积极态度
- WASM技术栈获得广泛认可
- 对隐私保护和数据主权的关注日益增强

### C. References

**技术文档**:
- [SQLite WASM官方文档](https://sqlite.org/wasm/)
- [OpenRouter API文档](https://openrouter.ai/docs)
- [React 18官方指南](https://react.dev/)

**竞品研究**:
- [Obsidian功能分析](https://obsidian.md/)
- [Logseq架构研究](https://logseq.com/)
- [Roam Research用户反馈](https://roamresearch.com/)

**相关项目**:
- [TiddlyWiki](https://tiddlywiki.com/) - 非线性文档工具
- [Foam](https://foambubble.github.io/) - VS Code知识管理
- [Athens Research](https://github.com/athensresearch/athens) - 开源Roam替代

## Next Steps

### Immediate Actions
1. **技术原型验证**: 创建SQLite WASM + 基础卡片功能的最小原型，验证核心技术可行性
2. **用户访谈**: 联系3-5个小程序员团队，验证需求假设和功能优先级
3. **竞品深度体验**: 深度使用Obsidian、Logseq等工具，识别改进机会
4. **技术栈最终确认**: 基于原型结果确认React、SQLite WASM等技术选择
5. **项目仓库建立**: 创建GitHub仓库，设置基础开发环境和CI/CD流程

### PM Handoff

这份项目简报为**本地卡片画布与AI交互工具**提供了完整的背景信息。请以'PRD生成模式'开始，仔细审查简报内容，与用户逐步创建PRD的各个部分，根据模板指示询问必要的澄清或建议改进。

项目已经过深度需求挖掘和批判性分析，技术架构和用户需求都有清晰定义。重点关注MVP范围的精确定义和技术实现的可行性验证。
