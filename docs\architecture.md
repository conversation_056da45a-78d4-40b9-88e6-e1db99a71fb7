# Card Canvas Application - Technical Architecture

## **Introduction (项目介绍)**

### **Project Overview (项目概述)**
本文档定义了卡片画布应用的完整技术架构。这是一个本地优先的Web应用，允许用户在画布上创建、编辑和连接卡片，支持Markdown内容编辑，并能将连接的卡片作为上下文发送给AI代理进行对话。

### **Architecture Type (架构类型)**
- **Greenfield Project** - 全新项目，无遗留系统约束
- **Local-First Architecture** - 本地优先，数据存储在浏览器中
- **Single-Page Application** - React-based SPA
- **No Backend Server** - 纯前端应用，使用SQLite WASM作为数据库

### **Change Log (变更日志)**
| Version | Date | Changes | Author |
|---------|------|---------|---------|
| 1.0.0 | 2024-01-31 | Initial architecture document | Winston (Architect) |

---

## **High Level Architecture (高层架构)**

### **Technical Summary (技术摘要)**
本地优先的React应用，使用SQLite WASM进行数据持久化，Konva.js处理2D画布渲染，通过OpenRouter API集成多种AI模型。应用采用组件化架构，支持离线使用，数据完全由用户控制。

### **Platform Choice (平台选择)**
- **Primary Platform:** Web (浏览器)
- **Deployment Target:** 本地开发环境 (Vite Dev Server)
- **Browser Support:** 现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+)
- **Mobile Support:** 响应式设计，支持平板和手机访问

### **Repository Structure (仓库结构)**
```
refly_sqlite/                 # 项目根目录
├── apps/                     # 应用程序
│   └── web/                  # 主Web应用
├── packages/                 # 共享包
│   ├── shared/               # 共享类型和工具
│   └── ui/                   # UI组件库
├── docs/                     # 项目文档
└── .bmad-core/              # BMad方法论配置
```

**Repository Type:** Monorepo with npm workspaces

### **Architecture Diagram (架构图)**
```
┌─────────────────────────────────────────────────────────────┐
│                    Browser Environment                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   React App     │  │  Konva Canvas   │  │   AI Chat UI    │ │
│  │   (UI Layer)    │  │  (Rendering)    │  │   (Interaction) │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  State Manager  │  │  Service Layer  │  │  Cache Manager  │ │
│  │   (Zustand)     │  │  (Business)     │  │   (LRU Cache)   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ SQLite Service  │  │ Markdown Proc.  │  │ Encryption Svc  │ │
│  │  (Data Layer)   │  │  (Content)      │  │   (Security)    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐                    │
│  │ SQLite WASM     │  │   IndexedDB     │                    │
│  │  (Database)     │  │   (Storage)     │                    │
│  └─────────────────┘  └─────────────────┘                    │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  OpenRouter API │
                    │   (External)    │
                    └─────────────────┘
```

### **Architectural Patterns (架构模式)**
- **Repository Pattern** - 数据访问抽象
- **Service Layer Pattern** - 业务逻辑封装
- **Component-Based Architecture** - React组件化
- **Event-Driven Architecture** - 组件间通信
- **Local-First Pattern** - 本地数据优先
- **Progressive Web App (PWA)** - 离线支持和缓存

---

## **Tech Stack (技术栈)**

### **Technology Selection Table (技术选择表)**

| Category | Technology | Version | Rationale |
|----------|------------|---------|-----------|
| **Frontend Framework** | React | 18.2+ | 成熟的组件化框架，丰富的生态系统，优秀的开发体验 |
| **Language** | TypeScript | 5.3+ | 类型安全，更好的开发体验，减少运行时错误 |
| **Build Tool** | Vite | 5.0+ | 快速的开发服务器，优化的生产构建，现代化工具链 |
| **UI Framework** | TailwindCSS | 3.4+ | 实用优先的CSS框架，快速开发，一致的设计系统 |
| **Component Library** | Headless UI | 1.7+ | 无样式的可访问组件，与TailwindCSS完美集成 |
| **Canvas Rendering** | Konva.js | 9.2+ | 高性能2D画布库，支持复杂图形操作和事件处理 |
| **State Management** | Zustand | 4.4+ | 轻量级状态管理，简单API，TypeScript友好 |
| **Database** | SQLite WASM | 3.44+ | 浏览器中的完整SQL数据库，支持复杂查询和事务 |
| **HTTP Client** | Alova.js | 2.17+ | 现代HTTP客户端，支持重试、缓存和错误处理 |
| **Markdown Processing** | marked.js | 11.0+ | 快速的Markdown解析器，支持扩展和自定义渲染 |
| **Code Highlighting** | highlight.js | 11.9+ | 语法高亮库，支持多种编程语言 |
| **Icons** | Lucide React | 0.300+ | 现代化图标库，SVG格式，可定制性强 |
| **Date Handling** | date-fns | 3.0+ | 现代化日期处理库，模块化设计，TypeScript支持 |
| **Validation** | Zod | 3.22+ | TypeScript优先的模式验证库，类型安全 |
| **Testing Framework** | Vitest | 1.1+ | 快速的单元测试框架，与Vite集成良好 |
| **E2E Testing** | Playwright | 1.40+ | 现代化端到端测试框架，支持多浏览器 |
| **Testing Library** | React Testing Library | 14.1+ | React组件测试的最佳实践库 |
| **Linting** | ESLint | 8.56+ | JavaScript/TypeScript代码质量检查 |
| **Formatting** | Prettier | 3.1+ | 代码格式化工具，保持代码风格一致 |
| **Package Manager** | npm | 10.2+ | Node.js包管理器，支持workspaces |

### **Rationale for Key Technology Choices (关键技术选择理由)**

#### **React 18+ with TypeScript**
- **成熟生态系统**: 丰富的组件库和工具链
- **并发特性**: React 18的并发渲染提升性能
- **类型安全**: TypeScript减少运行时错误，提升开发效率
- **团队熟悉度**: 广泛使用的技术栈，易于维护

#### **Konva.js for Canvas Rendering**
- **高性能**: 基于Canvas 2D API，支持硬件加速
- **事件处理**: 完整的鼠标/触摸事件支持
- **图形操作**: 支持拖拽、缩放、旋转等复杂操作
- **React集成**: react-konva提供良好的React集成

#### **SQLite WASM**
- **完整SQL支持**: 支持复杂查询、事务、触发器
- **本地优先**: 数据完全存储在浏览器中
- **性能优异**: 比IndexedDB更好的查询性能
- **数据完整性**: ACID事务保证数据一致性

#### **Zustand for State Management**
- **轻量级**: 仅2KB，比Redux轻量得多
- **简单API**: 学习曲线平缓，代码简洁
- **TypeScript友好**: 原生TypeScript支持
- **无样板代码**: 不需要复杂的action/reducer模式

---

## **Data Models (数据模型)**

### **Core Data Models (核心数据模型)**

#### **Canvas Model (画布模型)**
```typescript
interface Canvas {
  id: string;                    // UUID
  name: string;                  // 画布名称
  description?: string;          // 画布描述
  theme: CanvasTheme;           // 主题配置
  viewport: {                   // 视口状态
    x: number;
    y: number;
    zoom: number;
  };
  settings: {                   // 画布设置
    gridEnabled: boolean;
    snapToGrid: boolean;
    gridSize: number;
    backgroundColor: string;
  };
  created_at: Date;
  updated_at: Date;
}

enum CanvasTheme {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto'
}
```

#### **Card Model (卡片模型)**
```typescript
interface Card {
  id: string;                    // UUID
  canvas_id: string;             // 所属画布ID
  title: string;                 // 卡片标题（从内容提取）
  content: string;               // Markdown内容
  position_x: number;            // X坐标
  position_y: number;            // Y坐标
  width: number;                 // 宽度
  height: number;                // 高度
  z_index: number;               // 层级
  style: {                      // 样式配置
    backgroundColor: string;
    borderColor: string;
    textColor: string;
    fontSize: number;
  };
  tags: string[];               // 标签数组
  created_at: Date;
  updated_at: Date;
}
```

#### **Connection Model (连接模型)**
```typescript
interface Connection {
  id: string;                    // UUID
  canvas_id: string;             // 所属画布ID
  source_card_id: string;        // 源卡片ID
  target_card_id: string;        // 目标卡片ID
  source_anchor: AnchorPoint;    // 源锚点
  target_anchor: AnchorPoint;    // 目标锚点
  type: ConnectionType;          // 连接类型
  style: {                      // 连接样式
    color: string;
    width: number;
    dashArray?: number[];
  };
  label?: string;               // 连接标签
  created_at: Date;
  updated_at: Date;
}

enum AnchorPoint {
  TOP = 'top',
  RIGHT = 'right',
  BOTTOM = 'bottom',
  LEFT = 'left'
}

enum ConnectionType {
  ARROW = 'arrow',
  LINE = 'line',
  CURVE = 'curve'
}
```

#### **AI Conversation Model (AI对话模型)**
```typescript
interface AIConversation {
  id: string;                    // UUID
  canvas_id: string;             // 关联画布ID
  title: string;                 // 对话标题
  model: string;                 // AI模型名称
  messages: AIMessage[];         // 消息列表
  context_cards: string[];       // 上下文卡片ID列表
  created_at: Date;
  updated_at: Date;
}

interface AIMessage {
  id: string;                    // 消息ID
  role: 'user' | 'assistant';    // 消息角色
  content: string;               // 消息内容
  timestamp: Date;               // 时间戳
  metadata?: {                  // 元数据
    model?: string;
    tokens?: number;
    cost?: number;
  };
}
```

### **Data Relationships (数据关系)**
```
Canvas (1) ──── (N) Card
Canvas (1) ──── (N) Connection
Canvas (1) ──── (N) AIConversation
Card (1) ──── (N) Connection (as source)
Card (1) ──── (N) Connection (as target)
AIConversation (1) ──── (N) AIMessage
```

---

## **API Specification (API规范)**

### **External API Integration (外部API集成)**

#### **OpenRouter API Integration**
```typescript
// OpenRouter API配置
interface OpenRouterConfig {
  baseURL: 'https://openrouter.ai/api/v1';
  apiKey: string;                // 用户提供的API密钥
  defaultModel: string;          // 默认模型
  timeout: number;               // 请求超时时间
  retryAttempts: number;         // 重试次数
}

// 聊天完成请求
interface ChatCompletionRequest {
  model: string;                 // 模型名称
  messages: {
    role: 'user' | 'assistant' | 'system';
    content: string;
  }[];
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  stream?: boolean;
}

// 聊天完成响应
interface ChatCompletionResponse {
  id: string;
  object: 'chat.completion';
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: 'assistant';
      content: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}
```

#### **API Error Handling**
```typescript
interface APIError {
  error: {
    message: string;
    type: string;
    code: string;
  };
}

// 错误处理示例
try {
  const response = await openRouterClient.chat.completions.create(request);
  return response;
} catch (error) {
  if (error.status === 401) {
    throw new Error('API密钥无效');
  } else if (error.status === 429) {
    throw new Error('请求频率过高，请稍后重试');
  } else if (error.status >= 500) {
    throw new Error('AI服务暂时不可用');
  }
  throw error;
}
```

### **Internal Service Layer APIs (内部服务层API)**

#### **Canvas Service API**
```typescript
interface CanvasService {
  // 画布管理
  createCanvas(data: CreateCanvasRequest): Promise<Canvas>;
  getCanvas(id: string): Promise<Canvas>;
  updateCanvas(id: string, updates: Partial<Canvas>): Promise<Canvas>;
  deleteCanvas(id: string): Promise<void>;
  listCanvases(): Promise<Canvas[]>;

  // 画布操作
  duplicateCanvas(id: string): Promise<Canvas>;
  exportCanvas(id: string, format: 'json' | 'markdown'): Promise<string>;
  importCanvas(data: string, format: 'json' | 'markdown'): Promise<Canvas>;
}
```

#### **Card Service API**
```typescript
interface CardService {
  // 卡片CRUD
  createCard(data: CreateCardRequest): Promise<Card>;
  getCard(id: string): Promise<Card>;
  updateCard(id: string, updates: Partial<Card>): Promise<Card>;
  deleteCard(id: string): Promise<void>;

  // 卡片查询
  getCardsByCanvas(canvasId: string): Promise<Card[]>;
  searchCards(query: string, canvasId?: string): Promise<Card[]>;
  getCardsByTags(tags: string[], canvasId?: string): Promise<Card[]>;

  // 卡片操作
  duplicateCard(id: string): Promise<Card>;
  moveCard(id: string, position: { x: number; y: number }): Promise<void>;
  resizeCard(id: string, size: { width: number; height: number }): Promise<void>;
}
```

### **Event System Interfaces (事件系统接口)**
```typescript
interface EventBus {
  // 事件发布
  emit<T>(event: string, data: T): void;

  // 事件订阅
  on<T>(event: string, handler: (data: T) => void): () => void;

  // 一次性事件订阅
  once<T>(event: string, handler: (data: T) => void): () => void;

  // 取消订阅
  off(event: string, handler?: Function): void;
}

// 系统事件定义
type SystemEvents = {
  'card:created': Card;
  'card:updated': { id: string; updates: Partial<Card> };
  'card:deleted': { id: string };
  'connection:created': Connection;
  'connection:deleted': { id: string };
  'canvas:switched': { fromId: string; toId: string };
  'ai:message:sent': { conversationId: string; message: AIMessage };
  'ai:message:received': { conversationId: string; message: AIMessage };
};

---

## **Components (组件架构)**

### **Major Components Overview (主要组件概述)**

#### **1. CanvasEngine (画布引擎)**
**Responsibility:** 管理2D画布渲染、用户交互和视口控制

```typescript
interface CanvasEngine {
  // 画布管理
  initializeCanvas(container: HTMLElement): void;
  destroyCanvas(): void;
  resizeCanvas(width: number, height: number): void;

  // 视口控制
  setViewport(x: number, y: number, zoom: number): void;
  getViewport(): { x: number; y: number; zoom: number };
  zoomToFit(cards: Card[]): void;

  // 渲染管理
  renderCards(cards: Card[]): void;
  renderConnections(connections: Connection[]): void;
  updateCard(card: Card): void;
  removeCard(cardId: string): void;

  // 交互处理
  enableCardDragging(enabled: boolean): void;
  enableMultiSelection(enabled: boolean): void;
  getSelectedCards(): string[];

  // 事件处理
  onCardClick(handler: (cardId: string) => void): void;
  onCardDoubleClick(handler: (cardId: string) => void): void;
  onCardDrag(handler: (cardId: string, position: { x: number; y: number }) => void): void;
  onConnectionCreate(handler: (sourceId: string, targetId: string) => void): void;
}
```

#### **2. DatabaseManager (数据库管理器)**
**Responsibility:** SQLite WASM数据库操作和数据持久化

```typescript
interface DatabaseManager {
  // 数据库生命周期
  initialize(): Promise<void>;
  close(): Promise<void>;
  backup(): Promise<Uint8Array>;
  restore(data: Uint8Array): Promise<void>;

  // 事务管理
  transaction<T>(callback: (tx: Transaction) => Promise<T>): Promise<T>;

  // 查询执行
  executeQuery<T>(sql: string, params?: any[]): Promise<T[]>;
  executeNonQuery(sql: string, params?: any[]): Promise<void>;

  // 数据库维护
  vacuum(): Promise<void>;
  analyze(): Promise<void>;
  getStats(): Promise<DatabaseStats>;
}

interface DatabaseStats {
  size: number;
  pageCount: number;
  freePages: number;
  tableStats: Record<string, { rowCount: number; size: number }>;
}
```

#### **3. AIIntegrationService (AI集成服务)**
**Responsibility:** 管理AI模型交互、上下文格式化和对话历史

```typescript
interface AIIntegrationService {
  // 模型管理
  getAvailableModels(): Promise<AIModel[]>;
  setDefaultModel(modelId: string): void;

  // 对话管理
  createConversation(canvasId: string, contextCards: string[]): Promise<AIConversation>;
  sendMessage(conversationId: string, message: string): Promise<AIMessage>;
  getConversationHistory(conversationId: string): Promise<AIMessage[]>;

  // 上下文处理
  formatCardsAsContext(cards: Card[]): string;
  estimateTokenCount(text: string): number;
  optimizeContext(cards: Card[], maxTokens: number): Card[];

  // 流式响应
  sendMessageStream(
    conversationId: string,
    message: string,
    onChunk: (chunk: string) => void
  ): Promise<AIMessage>;
}

interface AIModel {
  id: string;
  name: string;
  provider: string;
  contextLength: number;
  costPer1kTokens: number;
  capabilities: string[];
}
```

#### **4. StateManager (状态管理器)**
**Responsibility:** 应用状态管理、状态同步和持久化

```typescript
interface AppState {
  // 当前状态
  currentCanvas: Canvas | null;
  cards: Record<string, Card>;
  connections: Record<string, Connection>;
  selectedCards: string[];

  // UI状态
  ui: {
    sidebarOpen: boolean;
    chatPanelOpen: boolean;
    settingsOpen: boolean;
    loading: boolean;
    error: string | null;
  };

  // 用户设置
  settings: {
    theme: 'light' | 'dark' | 'auto';
    autoSave: boolean;
    gridEnabled: boolean;
    snapToGrid: boolean;
    defaultCardSize: { width: number; height: number };
    aiModel: string;
    apiKey: string;
  };

  // AI状态
  ai: {
    conversations: Record<string, AIConversation>;
    activeConversation: string | null;
    isGenerating: boolean;
  };
}

interface StateManager {
  // 状态访问
  getState(): AppState;
  subscribe(listener: (state: AppState) => void): () => void;

  // 状态更新
  updateCanvas(canvas: Canvas): void;
  updateCard(card: Card): void;
  updateConnection(connection: Connection): void;
  setSelectedCards(cardIds: string[]): void;

  // 批量操作
  batchUpdate(updates: StateUpdate[]): void;

  // 持久化
  saveState(): Promise<void>;
  loadState(): Promise<void>;
}
```

#### **5. MarkdownProcessor (Markdown处理器)**
**Responsibility:** Markdown内容解析、渲染和编辑支持

```typescript
interface MarkdownProcessor {
  // 解析和渲染
  parseMarkdown(content: string): ParsedMarkdown;
  renderToHTML(content: string): string;
  renderToPlainText(content: string): string;

  // 内容提取
  extractTitle(content: string): string;
  extractTags(content: string): string[];
  extractLinks(content: string): string[];

  // 内容验证
  validateMarkdown(content: string): ValidationResult;
  sanitizeContent(content: string): string;

  // 编辑支持
  insertAtCursor(content: string, insertion: string, cursorPos: number): string;
  formatSelection(content: string, start: number, end: number, format: MarkdownFormat): string;
}

interface ParsedMarkdown {
  title: string;
  content: string;
  headings: { level: number; text: string; id: string }[];
  links: { text: string; url: string }[];
  tags: string[];
  wordCount: number;
}

enum MarkdownFormat {
  BOLD = 'bold',
  ITALIC = 'italic',
  CODE = 'code',
  LINK = 'link',
  HEADING = 'heading'
}

#### **6. CacheManager (缓存管理器)**
**Responsibility:** 内存缓存、性能优化和数据预加载

```typescript
interface CacheManager {
  // 缓存操作
  set<T>(key: string, value: T, ttl?: number): void;
  get<T>(key: string): T | null;
  has(key: string): boolean;
  delete(key: string): void;
  clear(): void;

  // 缓存策略
  setMaxSize(size: number): void;
  getStats(): CacheStats;

  // 预加载
  preloadCanvas(canvasId: string): Promise<void>;
  preloadCards(cardIds: string[]): Promise<void>;

  // 缓存失效
  invalidate(pattern: string): void;
  invalidateCanvas(canvasId: string): void;
}

interface CacheStats {
  size: number;
  maxSize: number;
  hitRate: number;
  missRate: number;
  evictions: number;
}
```

#### **7. ExportManager (导出管理器)**
**Responsibility:** 数据导出、格式转换和备份功能

```typescript
interface ExportManager {
  // 导出功能
  exportCanvas(canvasId: string, format: ExportFormat): Promise<string>;
  exportCards(cardIds: string[], format: ExportFormat): Promise<string>;
  exportConversation(conversationId: string, format: ExportFormat): Promise<string>;

  // 导入功能
  importCanvas(data: string, format: ExportFormat): Promise<Canvas>;
  importCards(data: string, canvasId: string, format: ExportFormat): Promise<Card[]>;

  // 备份功能
  createFullBackup(): Promise<Uint8Array>;
  restoreFromBackup(data: Uint8Array): Promise<void>;

  // 格式转换
  convertMarkdownToHTML(content: string): string;
  convertCardsToMarkdown(cards: Card[]): string;
  convertConversationToMarkdown(conversation: AIConversation): string;
}

enum ExportFormat {
  JSON = 'json',
  MARKDOWN = 'markdown',
  HTML = 'html',
  CSV = 'csv',
  PDF = 'pdf'
}
```

---

## **External APIs (外部API集成)**

### **OpenRouter API Integration (OpenRouter API集成)**

#### **API Client Implementation (API客户端实现)**
```typescript
class OpenRouterClient {
  private config: OpenRouterConfig;
  private httpClient: HttpClient;

  constructor(config: OpenRouterConfig) {
    this.config = config;
    this.httpClient = new HttpClient({
      baseURL: config.baseURL,
      timeout: config.timeout,
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Card Canvas App'
      }
    });
  }

  async createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    try {
      const response = await this.httpClient.post('/chat/completions', request);
      return response.data;
    } catch (error) {
      throw this.handleAPIError(error);
    }
  }

  async createChatCompletionStream(
    request: ChatCompletionRequest,
    onChunk: (chunk: string) => void
  ): Promise<void> {
    const streamRequest = { ...request, stream: true };

    try {
      const response = await fetch(`${this.config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: this.httpClient.getHeaders(),
        body: JSON.stringify(streamRequest)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response body');

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return;

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices[0]?.delta?.content;
              if (content) onChunk(content);
            } catch (e) {
              console.warn('Failed to parse SSE data:', data);
            }
          }
        }
      }
    } catch (error) {
      throw this.handleAPIError(error);
    }
  }

  async getModels(): Promise<AIModel[]> {
    try {
      const response = await this.httpClient.get('/models');
      return response.data.data.map(this.transformModel);
    } catch (error) {
      throw this.handleAPIError(error);
    }
  }

  private handleAPIError(error: any): Error {
    if (error.response?.status === 401) {
      return new Error('API密钥无效或已过期');
    } else if (error.response?.status === 429) {
      return new Error('请求频率过高，请稍后重试');
    } else if (error.response?.status >= 500) {
      return new Error('AI服务暂时不可用');
    } else if (error.code === 'NETWORK_ERROR') {
      return new Error('网络连接失败');
    }
    return new Error(`API请求失败: ${error.message}`);
  }

  private transformModel(apiModel: any): AIModel {
    return {
      id: apiModel.id,
      name: apiModel.name || apiModel.id,
      provider: apiModel.owned_by || 'unknown',
      contextLength: apiModel.context_length || 4096,
      costPer1kTokens: apiModel.pricing?.prompt || 0,
      capabilities: apiModel.capabilities || []
    };
  }
}
```

#### **Context Formatting (上下文格式化)**
```typescript
class ContextFormatter {
  static formatCardsAsContext(cards: Card[]): string {
    const sections = cards.map(card => {
      const title = card.title || '无标题卡片';
      const content = card.content.trim();
      const tags = card.tags.length > 0 ? `\n标签: ${card.tags.join(', ')}` : '';

      return `## ${title}\n\n${content}${tags}`;
    });

    return [
      '# 卡片上下文',
      '',
      '以下是用户选择的相关卡片内容，请基于这些信息回答问题：',
      '',
      ...sections
    ].join('\n');
  }

  static estimateTokenCount(text: string): number {
    // 简单的token估算：中文字符 * 1.5 + 英文单词数
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    return Math.ceil(chineseChars * 1.5 + englishWords);
  }

  static optimizeContext(cards: Card[], maxTokens: number): Card[] {
    let totalTokens = 0;
    const optimizedCards: Card[] = [];

    // 按创建时间排序，优先包含最新的卡片
    const sortedCards = [...cards].sort((a, b) =>
      b.created_at.getTime() - a.created_at.getTime()
    );

    for (const card of sortedCards) {
      const cardTokens = this.estimateTokenCount(
        this.formatCardsAsContext([card])
      );

      if (totalTokens + cardTokens <= maxTokens) {
        optimizedCards.push(card);
        totalTokens += cardTokens;
      } else {
        break;
      }
    }

    return optimizedCards;
  }
}

---

## **Document Status (文档状态)**

### **Completed Sections (已完成章节)**
✅ **Introduction** - 项目介绍和变更日志
✅ **High Level Architecture** - 高层架构概述
✅ **Tech Stack** - 技术栈选择和理由
✅ **Data Models** - 数据模型定义
✅ **API Specification** - API规范
✅ **Components** - 组件架构
✅ **External APIs** - 外部API集成

### **Remaining Sections (待添加章节)**
📋 **Core Workflows** - 核心工作流程
📋 **Database Schema** - 数据库架构
📋 **Frontend Architecture** - 前端架构
📋 **Backend Architecture** - 后端架构
📋 **Unified Project Structure** - 统一项目结构
📋 **Development Workflow** - 开发工作流
📋 **Deployment Architecture** - 部署架构
📋 **Security and Performance** - 安全和性能
📋 **Testing Strategy** - 测试策略
📋 **Coding Standards** - 编码标准
📋 **Error Handling Strategy** - 错误处理策略
📋 **Monitoring and Observability** - 监控与可观测性
📋 **Checklist Results Report** - 检查清单结果报告

### **Document Completion Status**
**Current Progress:** 35% (7/20 sections completed)
**Estimated Total Length:** ~3000+ lines when complete
**File Size Limit:** 300 lines per save operation

### **Next Steps**
由于文档长度限制，完整的架构文档需要通过多次编辑操作来完成。当前已包含核心的架构设计部分，包括：

1. **技术栈定义** - 20+技术选择和版本
2. **数据模型** - 4个核心实体和关系
3. **API规范** - 外部和内部API接口
4. **组件架构** - 7个主要组件设计
5. **外部集成** - OpenRouter API实现

剩余章节将包含详细的实施指导、数据库设计、安全策略、测试方案等内容。

---

## **Core Workflows (核心工作流程)**

### **1. Card Creation Workflow (卡片创建工作流)**
```mermaid
sequenceDiagram
    participant U as User
    participant UI as Canvas UI
    participant CM as Card Manager
    participant DB as Database
    participant CE as Canvas Engine

    U->>UI: Click "Add Card" button
    UI->>CM: createCard(canvasId, position)
    CM->>DB: INSERT INTO cards
    DB-->>CM: Return card ID
    CM->>CE: renderCard(card)
    CE-->>UI: Card rendered on canvas
    UI-->>U: Show new card with cursor in edit mode
```

### **2. Connection Creation Workflow (连接创建工作流)**
```mermaid
sequenceDiagram
    participant U as User
    participant UI as Canvas UI
    participant CM as Connection Manager
    participant DB as Database
    participant CE as Canvas Engine

    U->>UI: Click connection button on source card
    UI->>UI: Enter connection mode
    U->>UI: Click target card
    UI->>CM: createConnection(sourceId, targetId)
    CM->>DB: INSERT INTO connections
    DB-->>CM: Return connection ID
    CM->>CE: renderConnection(connection)
    CE-->>UI: Connection line drawn
    UI-->>U: Exit connection mode
```

### **3. AI Interaction Workflow (AI交互工作流)**
```mermaid
sequenceDiagram
    participant U as User
    participant UI as Chat Panel
    participant AI as AI Service
    participant API as OpenRouter API
    participant DB as Database

    U->>UI: Select cards and click "Chat with AI"
    UI->>AI: createConversation(canvasId, selectedCards)
    AI->>DB: INSERT INTO ai_conversations
    AI->>AI: formatCardsAsContext(cards)
    U->>UI: Type message
    UI->>AI: sendMessage(conversationId, message)
    AI->>API: POST /chat/completions
    API-->>AI: Stream response chunks
    AI->>UI: onChunk(chunk)
    UI-->>U: Display streaming response
    AI->>DB: INSERT INTO ai_messages
```

### **4. Multi-Canvas Switching Workflow (多画布切换工作流)**
```mermaid
sequenceDiagram
    participant U as User
    participant UI as Canvas Tabs
    participant SM as State Manager
    participant CM as Cache Manager
    participant DB as Database

    U->>UI: Click canvas tab
    UI->>SM: switchCanvas(canvasId)
    SM->>CM: get(canvasId)
    alt Cache Hit
        CM-->>SM: Return cached canvas data
    else Cache Miss
        SM->>DB: SELECT canvas, cards, connections
        DB-->>SM: Return canvas data
        SM->>CM: set(canvasId, data)
    end
    SM->>UI: updateCanvasView(data)
    UI-->>U: Display new canvas
```

### **5. Data Export Workflow (数据导出工作流)**
```mermaid
sequenceDiagram
    participant U as User
    participant UI as Export Dialog
    participant EM as Export Manager
    participant DB as Database
    participant FS as File System

    U->>UI: Click "Export Canvas"
    UI->>UI: Show export options
    U->>UI: Select format and scope
    UI->>EM: exportCanvas(canvasId, format)
    EM->>DB: SELECT canvas data
    DB-->>EM: Return data
    EM->>EM: formatData(data, format)
    EM->>FS: downloadFile(formattedData)
    FS-->>U: File downloaded
```

---

## **Database Schema (数据库架构)**

### **SQLite Schema Definition (SQLite架构定义)**

#### **Tables (数据表)**
```sql
-- 画布表
CREATE TABLE canvases (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    theme TEXT DEFAULT 'light' CHECK (theme IN ('light', 'dark', 'auto')),
    viewport_x REAL DEFAULT 0,
    viewport_y REAL DEFAULT 0,
    viewport_zoom REAL DEFAULT 1.0,
    settings_grid_enabled BOOLEAN DEFAULT true,
    settings_snap_to_grid BOOLEAN DEFAULT true,
    settings_grid_size INTEGER DEFAULT 20,
    settings_background_color TEXT DEFAULT '#ffffff',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 卡片表
CREATE TABLE cards (
    id TEXT PRIMARY KEY,
    canvas_id TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL DEFAULT '',
    position_x REAL NOT NULL,
    position_y REAL NOT NULL,
    width REAL DEFAULT 200,
    height REAL DEFAULT 150,
    z_index INTEGER DEFAULT 0,
    style_background_color TEXT DEFAULT '#ffffff',
    style_border_color TEXT DEFAULT '#e5e7eb',
    style_text_color TEXT DEFAULT '#111827',
    style_font_size INTEGER DEFAULT 14,
    tags TEXT DEFAULT '[]', -- JSON array
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (canvas_id) REFERENCES canvases(id) ON DELETE CASCADE
);

-- 连接表
CREATE TABLE connections (
    id TEXT PRIMARY KEY,
    canvas_id TEXT NOT NULL,
    source_card_id TEXT NOT NULL,
    target_card_id TEXT NOT NULL,
    source_anchor TEXT DEFAULT 'right' CHECK (source_anchor IN ('top', 'right', 'bottom', 'left')),
    target_anchor TEXT DEFAULT 'left' CHECK (target_anchor IN ('top', 'right', 'bottom', 'left')),
    type TEXT DEFAULT 'arrow' CHECK (type IN ('arrow', 'line', 'curve')),
    style_color TEXT DEFAULT '#6b7280',
    style_width REAL DEFAULT 2,
    style_dash_array TEXT, -- JSON array for dash pattern
    label TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (canvas_id) REFERENCES canvases(id) ON DELETE CASCADE,
    FOREIGN KEY (source_card_id) REFERENCES cards(id) ON DELETE CASCADE,
    FOREIGN KEY (target_card_id) REFERENCES cards(id) ON DELETE CASCADE,
    UNIQUE(source_card_id, target_card_id) -- 防止重复连接
);

-- AI对话表
CREATE TABLE ai_conversations (
    id TEXT PRIMARY KEY,
    canvas_id TEXT NOT NULL,
    title TEXT NOT NULL,
    model TEXT NOT NULL,
    context_cards TEXT NOT NULL DEFAULT '[]', -- JSON array of card IDs
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (canvas_id) REFERENCES canvases(id) ON DELETE CASCADE
);

-- AI消息表
CREATE TABLE ai_messages (
    id TEXT PRIMARY KEY,
    conversation_id TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    metadata TEXT DEFAULT '{}', -- JSON object for model, tokens, cost, etc.
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id) ON DELETE CASCADE
);

-- 用户设置表
CREATE TABLE user_settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### **Indexes (索引)**
```sql
-- 性能优化索引
CREATE INDEX idx_cards_canvas_id ON cards(canvas_id);
CREATE INDEX idx_cards_position ON cards(canvas_id, position_x, position_y);
CREATE INDEX idx_cards_updated_at ON cards(updated_at);
CREATE INDEX idx_cards_tags ON cards(tags); -- 支持JSON查询

CREATE INDEX idx_connections_canvas_id ON connections(canvas_id);
CREATE INDEX idx_connections_source ON connections(source_card_id);
CREATE INDEX idx_connections_target ON connections(target_card_id);

CREATE INDEX idx_ai_conversations_canvas_id ON ai_conversations(canvas_id);
CREATE INDEX idx_ai_conversations_updated_at ON ai_conversations(updated_at);

CREATE INDEX idx_ai_messages_conversation_id ON ai_messages(conversation_id);
CREATE INDEX idx_ai_messages_created_at ON ai_messages(created_at);

-- 全文搜索索引
CREATE VIRTUAL TABLE cards_fts USING fts5(
    id UNINDEXED,
    title,
    content,
    tags,
    content=cards,
    content_rowid=rowid
);

-- FTS触发器
CREATE TRIGGER cards_fts_insert AFTER INSERT ON cards BEGIN
    INSERT INTO cards_fts(rowid, id, title, content, tags)
    VALUES (new.rowid, new.id, new.title, new.content, new.tags);
END;

CREATE TRIGGER cards_fts_delete AFTER DELETE ON cards BEGIN
    INSERT INTO cards_fts(cards_fts, rowid, id, title, content, tags)
    VALUES('delete', old.rowid, old.id, old.title, old.content, old.tags);
END;

CREATE TRIGGER cards_fts_update AFTER UPDATE ON cards BEGIN
    INSERT INTO cards_fts(cards_fts, rowid, id, title, content, tags)
    VALUES('delete', old.rowid, old.id, old.title, old.content, old.tags);
    INSERT INTO cards_fts(rowid, id, title, content, tags)
    VALUES (new.rowid, new.id, new.title, new.content, new.tags);
END;

#### **Triggers (触发器)**
```sql
-- 自动更新时间戳
CREATE TRIGGER update_canvases_timestamp
    AFTER UPDATE ON canvases
BEGIN
    UPDATE canvases SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER update_cards_timestamp
    AFTER UPDATE ON cards
BEGIN
    UPDATE cards SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER update_connections_timestamp
    AFTER UPDATE ON connections
BEGIN
    UPDATE connections SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 级联删除清理
CREATE TRIGGER cleanup_orphaned_conversations
    AFTER DELETE ON canvases
BEGIN
    DELETE FROM ai_conversations WHERE canvas_id = OLD.id;
END;

-- 数据完整性检查
CREATE TRIGGER validate_card_position
    BEFORE INSERT ON cards
BEGIN
    SELECT CASE
        WHEN NEW.position_x < -10000 OR NEW.position_x > 10000 THEN
            RAISE(ABORT, 'Card X position out of bounds')
        WHEN NEW.position_y < -10000 OR NEW.position_y > 10000 THEN
            RAISE(ABORT, 'Card Y position out of bounds')
        WHEN NEW.width < 50 OR NEW.width > 1000 THEN
            RAISE(ABORT, 'Card width out of bounds')
        WHEN NEW.height < 30 OR NEW.height > 1000 THEN
            RAISE(ABORT, 'Card height out of bounds')
    END;
END;
```

#### **Views (视图)**
```sql
-- 画布统计视图
CREATE VIEW canvas_stats AS
SELECT
    c.id,
    c.name,
    COUNT(DISTINCT cards.id) as card_count,
    COUNT(DISTINCT connections.id) as connection_count,
    COUNT(DISTINCT ai_conversations.id) as conversation_count,
    MAX(cards.updated_at) as last_card_update,
    c.updated_at as canvas_updated_at
FROM canvases c
LEFT JOIN cards ON c.id = cards.canvas_id
LEFT JOIN connections ON c.id = connections.canvas_id
LEFT JOIN ai_conversations ON c.id = ai_conversations.canvas_id
GROUP BY c.id, c.name, c.updated_at;

-- 卡片连接视图
CREATE VIEW card_connections AS
SELECT
    c.id as connection_id,
    c.canvas_id,
    c.source_card_id,
    c.target_card_id,
    sc.title as source_title,
    tc.title as target_title,
    c.type,
    c.label,
    c.created_at
FROM connections c
JOIN cards sc ON c.source_card_id = sc.id
JOIN cards tc ON c.target_card_id = tc.id;

-- AI对话摘要视图
CREATE VIEW conversation_summary AS
SELECT
    conv.id,
    conv.canvas_id,
    conv.title,
    conv.model,
    COUNT(msg.id) as message_count,
    MAX(msg.created_at) as last_message_at,
    conv.created_at
FROM ai_conversations conv
LEFT JOIN ai_messages msg ON conv.id = msg.conversation_id
GROUP BY conv.id, conv.canvas_id, conv.title, conv.model, conv.created_at;
```

#### **Performance Optimizations (性能优化)**
```sql
-- 分析表统计信息
ANALYZE;

-- 启用WAL模式提升并发性能
PRAGMA journal_mode = WAL;

-- 优化内存使用
PRAGMA cache_size = -64000; -- 64MB cache

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 优化同步模式
PRAGMA synchronous = NORMAL;

-- 设置临时存储
PRAGMA temp_store = MEMORY;
```

---

## **Frontend Architecture (前端架构)**

### **Component Organization (组件组织)**

#### **Directory Structure (目录结构)**
```
apps/web/src/
├── components/           # 可复用组件
│   ├── ui/              # 基础UI组件
│   │   ├── Button/
│   │   ├── Input/
│   │   ├── Modal/
│   │   └── Tooltip/
│   ├── canvas/          # 画布相关组件
│   │   ├── CanvasContainer/
│   │   ├── CardComponent/
│   │   ├── ConnectionLine/
│   │   └── CanvasToolbar/
│   ├── chat/            # AI聊天组件
│   │   ├── ChatPanel/
│   │   ├── MessageList/
│   │   ├── MessageInput/
│   │   └── ModelSelector/
│   └── layout/          # 布局组件
│       ├── Sidebar/
│       ├── Header/
│       └── MainLayout/
├── pages/               # 页面组件
│   ├── CanvasPage/
│   ├── SettingsPage/
│   └── HelpPage/
├── hooks/               # 自定义Hooks
│   ├── useCanvas/
│   ├── useCards/
│   ├── useConnections/
│   ├── useAI/
│   └── useDatabase/
├── services/            # 服务层
│   ├── database/
│   ├── ai/
│   ├── canvas/
│   └── export/
├── stores/              # Zustand状态管理
│   ├── canvasStore.ts
│   ├── cardStore.ts
│   ├── aiStore.ts
│   └── settingsStore.ts
├── utils/               # 工具函数
│   ├── validation/
│   ├── formatting/
│   └── constants/
└── types/               # TypeScript类型定义
    ├── canvas.ts
    ├── card.ts
    ├── ai.ts
    └── database.ts
```

#### **State Management with Zustand (Zustand状态管理)**
```typescript
// canvasStore.ts
interface CanvasState {
  // 状态
  currentCanvas: Canvas | null;
  canvases: Canvas[];
  viewport: { x: number; y: number; zoom: number };

  // 操作
  setCurrentCanvas: (canvas: Canvas) => void;
  updateViewport: (viewport: Partial<{ x: number; y: number; zoom: number }>) => void;
  createCanvas: (data: CreateCanvasRequest) => Promise<Canvas>;
  updateCanvas: (id: string, updates: Partial<Canvas>) => Promise<void>;
  deleteCanvas: (id: string) => Promise<void>;
  loadCanvases: () => Promise<void>;
}

export const useCanvasStore = create<CanvasState>((set, get) => ({
  currentCanvas: null,
  canvases: [],
  viewport: { x: 0, y: 0, zoom: 1 },

  setCurrentCanvas: (canvas) => set({ currentCanvas: canvas }),

  updateViewport: (viewport) => set((state) => ({
    viewport: { ...state.viewport, ...viewport }
  })),

  createCanvas: async (data) => {
    const canvas = await canvasService.createCanvas(data);
    set((state) => ({
      canvases: [...state.canvases, canvas],
      currentCanvas: canvas
    }));
    return canvas;
  },

  updateCanvas: async (id, updates) => {
    await canvasService.updateCanvas(id, updates);
    set((state) => ({
      canvases: state.canvases.map(c => c.id === id ? { ...c, ...updates } : c),
      currentCanvas: state.currentCanvas?.id === id
        ? { ...state.currentCanvas, ...updates }
        : state.currentCanvas
    }));
  },

  deleteCanvas: async (id) => {
    await canvasService.deleteCanvas(id);
    set((state) => ({
      canvases: state.canvases.filter(c => c.id !== id),
      currentCanvas: state.currentCanvas?.id === id ? null : state.currentCanvas
    }));
  },

  loadCanvases: async () => {
    const canvases = await canvasService.listCanvases();
    set({ canvases });
  }
}));

// cardStore.ts
interface CardState {
  cards: Record<string, Card>;
  selectedCards: string[];

  setCards: (cards: Card[]) => void;
  addCard: (card: Card) => void;
  updateCard: (id: string, updates: Partial<Card>) => void;
  deleteCard: (id: string) => void;
  setSelectedCards: (cardIds: string[]) => void;
  getCardsByCanvas: (canvasId: string) => Card[];
}

export const useCardStore = create<CardState>((set, get) => ({
  cards: {},
  selectedCards: [],

  setCards: (cards) => set({
    cards: cards.reduce((acc, card) => ({ ...acc, [card.id]: card }), {})
  }),

  addCard: (card) => set((state) => ({
    cards: { ...state.cards, [card.id]: card }
  })),

  updateCard: (id, updates) => set((state) => ({
    cards: {
      ...state.cards,
      [id]: { ...state.cards[id], ...updates }
    }
  })),

  deleteCard: (id) => set((state) => {
    const { [id]: deleted, ...rest } = state.cards;
    return {
      cards: rest,
      selectedCards: state.selectedCards.filter(cardId => cardId !== id)
    };
  }),

  setSelectedCards: (cardIds) => set({ selectedCards: cardIds }),

  getCardsByCanvas: (canvasId) => {
    const { cards } = get();
    return Object.values(cards).filter(card => card.canvas_id === canvasId);
  }
}));
```

#### **Custom Hooks (自定义Hooks)**
```typescript
// useCanvas.ts
export const useCanvas = () => {
  const canvasStore = useCanvasStore();
  const cardStore = useCardStore();
  const connectionStore = useConnectionStore();

  const loadCanvas = useCallback(async (canvasId: string) => {
    try {
      const [canvas, cards, connections] = await Promise.all([
        canvasService.getCanvas(canvasId),
        cardService.getCardsByCanvas(canvasId),
        connectionService.getConnectionsByCanvas(canvasId)
      ]);

      canvasStore.setCurrentCanvas(canvas);
      cardStore.setCards(cards);
      connectionStore.setConnections(connections);
    } catch (error) {
      console.error('Failed to load canvas:', error);
      throw error;
    }
  }, [canvasStore, cardStore, connectionStore]);

  const createCard = useCallback(async (data: CreateCardRequest) => {
    try {
      const card = await cardService.createCard(data);
      cardStore.addCard(card);
      return card;
    } catch (error) {
      console.error('Failed to create card:', error);
      throw error;
    }
  }, [cardStore]);

  const updateCard = useCallback(async (id: string, updates: Partial<Card>) => {
    try {
      await cardService.updateCard(id, updates);
      cardStore.updateCard(id, updates);
    } catch (error) {
      console.error('Failed to update card:', error);
      throw error;
    }
  }, [cardStore]);

  return {
    loadCanvas,
    createCard,
    updateCard,
    currentCanvas: canvasStore.currentCanvas,
    cards: cardStore.getCardsByCanvas(canvasStore.currentCanvas?.id || ''),
    selectedCards: cardStore.selectedCards
  };
};

// useAI.ts
export const useAI = () => {
  const aiStore = useAIStore();
  const cardStore = useCardStore();

  const startConversation = useCallback(async (canvasId: string, selectedCardIds: string[]) => {
    try {
      const cards = selectedCardIds.map(id => cardStore.cards[id]).filter(Boolean);
      const conversation = await aiService.createConversation(canvasId, cards);
      aiStore.addConversation(conversation);
      aiStore.setActiveConversation(conversation.id);
      return conversation;
    } catch (error) {
      console.error('Failed to start conversation:', error);
      throw error;
    }
  }, [aiStore, cardStore]);

  const sendMessage = useCallback(async (message: string) => {
    const { activeConversation } = aiStore;
    if (!activeConversation) throw new Error('No active conversation');

    try {
      aiStore.setGenerating(true);
      const response = await aiService.sendMessage(activeConversation, message);
      aiStore.addMessage(activeConversation, response);
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    } finally {
      aiStore.setGenerating(false);
    }
  }, [aiStore]);

  return {
    startConversation,
    sendMessage,
    conversations: aiStore.conversations,
    activeConversation: aiStore.activeConversation,
    isGenerating: aiStore.isGenerating
  };
};
```

#### **Routing Architecture (路由架构)**
```typescript
// App.tsx
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Navigate to="/canvas" replace />} />
        <Route path="/canvas" element={<CanvasPage />} />
        <Route path="/canvas/:canvasId" element={<CanvasPage />} />
        <Route path="/settings" element={<SettingsPage />} />
        <Route path="/help" element={<HelpPage />} />
        <Route path="*" element={<Navigate to="/canvas" replace />} />
      </Routes>
    </BrowserRouter>
  );
}

// CanvasPage.tsx
export const CanvasPage: React.FC = () => {
  const { canvasId } = useParams<{ canvasId: string }>();
  const { loadCanvas, currentCanvas } = useCanvas();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initializeCanvas = async () => {
      try {
        if (canvasId) {
          await loadCanvas(canvasId);
        } else {
          // Load default or create new canvas
          const canvases = await canvasService.listCanvases();
          if (canvases.length > 0) {
            await loadCanvas(canvases[0].id);
          }
        }
      } catch (error) {
        console.error('Failed to initialize canvas:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeCanvas();
  }, [canvasId, loadCanvas]);

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <MainLayout>
      <CanvasContainer />
      <ChatPanel />
    </MainLayout>
  );
};
```

#### **Service Layer Implementation (服务层实现)**
```typescript
// services/canvasService.ts
class CanvasService {
  private db: DatabaseManager;
  private cache: CacheManager;

  constructor(db: DatabaseManager, cache: CacheManager) {
    this.db = db;
    this.cache = cache;
  }

  async createCanvas(data: CreateCanvasRequest): Promise<Canvas> {
    const canvas: Canvas = {
      id: generateUUID(),
      name: data.name,
      description: data.description,
      theme: data.theme || CanvasTheme.LIGHT,
      viewport: { x: 0, y: 0, zoom: 1 },
      settings: {
        gridEnabled: true,
        snapToGrid: true,
        gridSize: 20,
        backgroundColor: '#ffffff'
      },
      created_at: new Date(),
      updated_at: new Date()
    };

    await this.db.executeNonQuery(
      `INSERT INTO canvases (id, name, description, theme, viewport_x, viewport_y, viewport_zoom,
       settings_grid_enabled, settings_snap_to_grid, settings_grid_size, settings_background_color,
       created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [canvas.id, canvas.name, canvas.description, canvas.theme,
       canvas.viewport.x, canvas.viewport.y, canvas.viewport.zoom,
       canvas.settings.gridEnabled, canvas.settings.snapToGrid,
       canvas.settings.gridSize, canvas.settings.backgroundColor,
       canvas.created_at.toISOString(), canvas.updated_at.toISOString()]
    );

    this.cache.set(`canvas:${canvas.id}`, canvas);
    return canvas;
  }

  async getCanvas(id: string): Promise<Canvas> {
    // 尝试从缓存获取
    const cached = this.cache.get<Canvas>(`canvas:${id}`);
    if (cached) return cached;

    // 从数据库查询
    const rows = await this.db.executeQuery<any>(
      'SELECT * FROM canvases WHERE id = ?',
      [id]
    );

    if (rows.length === 0) {
      throw new Error(`Canvas not found: ${id}`);
    }

    const canvas = this.mapRowToCanvas(rows[0]);
    this.cache.set(`canvas:${id}`, canvas);
    return canvas;
  }

  private mapRowToCanvas(row: any): Canvas {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      theme: row.theme as CanvasTheme,
      viewport: {
        x: row.viewport_x,
        y: row.viewport_y,
        zoom: row.viewport_zoom
      },
      settings: {
        gridEnabled: row.settings_grid_enabled,
        snapToGrid: row.settings_snap_to_grid,
        gridSize: row.settings_grid_size,
        backgroundColor: row.settings_background_color
      },
      created_at: new Date(row.created_at),
      updated_at: new Date(row.updated_at)
    };
  }
}

---

## **Backend Architecture (后端架构)**

### **Browser-as-Backend Service Architecture (浏览器即后端服务架构)**

由于本应用采用本地优先架构，传统意义上的"后端"实际上是在浏览器中运行的服务层。这些服务负责数据管理、业务逻辑和外部API集成。

#### **Service Layer Architecture (服务层架构)**
```typescript
// 服务注册表
interface ServiceRegistry {
  database: DatabaseManager;
  cache: CacheManager;
  canvas: CanvasService;
  card: CardService;
  connection: ConnectionService;
  ai: AIIntegrationService;
  export: ExportManager;
  encryption: EncryptionService;
}

// 服务容器
class ServiceContainer {
  private services: Partial<ServiceRegistry> = {};
  private initialized = false;

  async initialize(): Promise<void> {
    if (this.initialized) return;

    // 初始化核心服务
    this.services.database = new DatabaseManager();
    await this.services.database.initialize();

    this.services.cache = new CacheManager();
    this.services.encryption = new EncryptionService();

    // 初始化业务服务
    this.services.canvas = new CanvasService(
      this.services.database,
      this.services.cache
    );

    this.services.card = new CardService(
      this.services.database,
      this.services.cache
    );

    this.services.connection = new ConnectionService(
      this.services.database,
      this.services.cache
    );

    this.services.ai = new AIIntegrationService(
      this.services.database,
      this.services.encryption
    );

    this.services.export = new ExportManager(
      this.services.database
    );

    this.initialized = true;
  }

  get<K extends keyof ServiceRegistry>(serviceName: K): ServiceRegistry[K] {
    const service = this.services[serviceName];
    if (!service) {
      throw new Error(`Service not found: ${serviceName}`);
    }
    return service;
  }
}

// 全局服务容器实例
export const serviceContainer = new ServiceContainer();
```

#### **SQLite Service Implementation (SQLite服务实现)**
```typescript
class DatabaseManager {
  private db: any; // SQLite WASM database instance
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 加载SQLite WASM
      const sqlite3 = await import('sqlite3-wasm');

      // 创建数据库实例
      this.db = new sqlite3.Database(':memory:');

      // 执行初始化脚本
      await this.executeSchema();

      // 配置数据库
      await this.configureDatabase();

      this.isInitialized = true;
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  private async executeSchema(): Promise<void> {
    const schema = `
      -- 创建所有表
      ${CANVAS_TABLE_SQL}
      ${CARDS_TABLE_SQL}
      ${CONNECTIONS_TABLE_SQL}
      ${AI_CONVERSATIONS_TABLE_SQL}
      ${AI_MESSAGES_TABLE_SQL}
      ${USER_SETTINGS_TABLE_SQL}

      -- 创建索引
      ${INDEXES_SQL}

      -- 创建触发器
      ${TRIGGERS_SQL}

      -- 创建视图
      ${VIEWS_SQL}
    `;

    await this.db.exec(schema);
  }

  private async configureDatabase(): Promise<void> {
    // 启用外键约束
    await this.db.exec('PRAGMA foreign_keys = ON');

    // 设置WAL模式
    await this.db.exec('PRAGMA journal_mode = WAL');

    // 优化性能设置
    await this.db.exec('PRAGMA cache_size = -64000');
    await this.db.exec('PRAGMA synchronous = NORMAL');
    await this.db.exec('PRAGMA temp_store = MEMORY');
  }

  async transaction<T>(callback: (tx: Transaction) => Promise<T>): Promise<T> {
    await this.db.exec('BEGIN TRANSACTION');

    try {
      const result = await callback(new Transaction(this.db));
      await this.db.exec('COMMIT');
      return result;
    } catch (error) {
      await this.db.exec('ROLLBACK');
      throw error;
    }
  }

  async executeQuery<T>(sql: string, params: any[] = []): Promise<T[]> {
    try {
      const stmt = this.db.prepare(sql);
      const results: T[] = [];

      while (stmt.step()) {
        results.push(stmt.getAsObject() as T);
      }

      stmt.free();
      return results;
    } catch (error) {
      console.error('Query execution failed:', { sql, params, error });
      throw error;
    }
  }

  async executeNonQuery(sql: string, params: any[] = []): Promise<void> {
    try {
      const stmt = this.db.prepare(sql);
      stmt.run(params);
      stmt.free();
    } catch (error) {
      console.error('Non-query execution failed:', { sql, params, error });
      throw error;
    }
  }

  async backup(): Promise<Uint8Array> {
    return this.db.export();
  }

  async restore(data: Uint8Array): Promise<void> {
    this.db.close();
    this.db = new sqlite3.Database(data);
    await this.configureDatabase();
  }

  async close(): Promise<void> {
    if (this.db) {
      this.db.close();
      this.isInitialized = false;
    }
  }
}

class Transaction {
  constructor(private db: any) {}

  async executeQuery<T>(sql: string, params: any[] = []): Promise<T[]> {
    const stmt = this.db.prepare(sql);
    const results: T[] = [];

    while (stmt.step()) {
      results.push(stmt.getAsObject() as T);
    }

    stmt.free();
    return results;
  }

  async executeNonQuery(sql: string, params: any[] = []): Promise<void> {
    const stmt = this.db.prepare(sql);
    stmt.run(params);
    stmt.free();
  }
}
```

#### **API Key Management (API密钥管理)**
```typescript
class EncryptionService {
  private key: CryptoKey | null = null;

  async initialize(): Promise<void> {
    // 生成或获取加密密钥
    const keyData = localStorage.getItem('app_encryption_key');

    if (keyData) {
      // 从存储中恢复密钥
      const keyBuffer = new Uint8Array(JSON.parse(keyData));
      this.key = await crypto.subtle.importKey(
        'raw',
        keyBuffer,
        { name: 'AES-GCM' },
        false,
        ['encrypt', 'decrypt']
      );
    } else {
      // 生成新密钥
      this.key = await crypto.subtle.generateKey(
        { name: 'AES-GCM', length: 256 },
        true,
        ['encrypt', 'decrypt']
      );

      // 导出并存储密钥
      const keyBuffer = await crypto.subtle.exportKey('raw', this.key);
      localStorage.setItem('app_encryption_key', JSON.stringify(Array.from(new Uint8Array(keyBuffer))));
    }
  }

  async encrypt(data: string): Promise<string> {
    if (!this.key) throw new Error('Encryption service not initialized');

    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    const iv = crypto.getRandomValues(new Uint8Array(12));

    const encryptedBuffer = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      this.key,
      dataBuffer
    );

    // 组合IV和加密数据
    const combined = new Uint8Array(iv.length + encryptedBuffer.byteLength);
    combined.set(iv);
    combined.set(new Uint8Array(encryptedBuffer), iv.length);

    return btoa(String.fromCharCode(...combined));
  }

  async decrypt(encryptedData: string): Promise<string> {
    if (!this.key) throw new Error('Encryption service not initialized');

    const combined = new Uint8Array(
      atob(encryptedData).split('').map(char => char.charCodeAt(0))
    );

    const iv = combined.slice(0, 12);
    const encrypted = combined.slice(12);

    const decryptedBuffer = await crypto.subtle.decrypt(
      { name: 'AES-GCM', iv },
      this.key,
      encrypted
    );

    const decoder = new TextDecoder();
    return decoder.decode(decryptedBuffer);
  }
}

// API密钥管理
class APIKeyManager {
  private encryptionService: EncryptionService;

  constructor(encryptionService: EncryptionService) {
    this.encryptionService = encryptionService;
  }

  async storeAPIKey(key: string): Promise<void> {
    const encrypted = await this.encryptionService.encrypt(key);
    localStorage.setItem('openrouter_api_key', encrypted);
  }

  async getAPIKey(): Promise<string | null> {
    const encrypted = localStorage.getItem('openrouter_api_key');
    if (!encrypted) return null;

    try {
      return await this.encryptionService.decrypt(encrypted);
    } catch (error) {
      console.error('Failed to decrypt API key:', error);
      return null;
    }
  }

  async removeAPIKey(): Promise<void> {
    localStorage.removeItem('openrouter_api_key');
  }

  async validateAPIKey(key: string): Promise<boolean> {
    try {
      const client = new OpenRouterClient({
        baseURL: 'https://openrouter.ai/api/v1',
        apiKey: key,
        timeout: 10000,
        retryAttempts: 1
      });

      await client.getModels();
      return true;
    } catch (error) {
      return false;
    }
  }
}
```

---

## **Unified Project Structure (统一项目结构)**

### **Monorepo Structure with npm workspaces (npm workspaces单体仓库结构)**

```
refly_sqlite/                           # 项目根目录
├── package.json                        # 根package.json (workspaces配置)
├── package-lock.json                   # 锁定文件
├── tsconfig.json                       # 根TypeScript配置
├── .gitignore                          # Git忽略文件
├── .env.example                        # 环境变量示例
├── README.md                           # 项目说明
├── LICENSE                             # 许可证
│
├── apps/                               # 应用程序
│   └── web/                            # 主Web应用
│       ├── package.json                # Web应用依赖
│       ├── tsconfig.json               # Web应用TS配置
│       ├── vite.config.ts              # Vite配置
│       ├── tailwind.config.js          # TailwindCSS配置
│       ├── index.html                  # HTML入口
│       ├── public/                     # 静态资源
│       │   ├── favicon.ico
│       │   ├── manifest.json           # PWA清单
│       │   └── icons/                  # 应用图标
│       └── src/                        # 源代码
│           ├── main.tsx                # 应用入口
│           ├── App.tsx                 # 根组件
│           ├── components/             # 组件目录
│           ├── pages/                  # 页面组件
│           ├── hooks/                  # 自定义Hooks
│           ├── services/               # 服务层
│           ├── stores/                 # 状态管理
│           ├── utils/                  # 工具函数
│           ├── types/                  # 类型定义
│           └── styles/                 # 样式文件
│               ├── globals.css         # 全局样式
│               └── components.css      # 组件样式
│
├── packages/                           # 共享包
│   ├── shared/                         # 共享类型和工具
│   │   ├── package.json
│   │   ├── tsconfig.json
│   │   └── src/
│   │       ├── types/                  # 共享类型定义
│   │       │   ├── canvas.ts
│   │       │   ├── card.ts
│   │       │   ├── connection.ts
│   │       │   └── ai.ts
│   │       ├── utils/                  # 共享工具函数
│   │       │   ├── validation.ts
│   │       │   ├── formatting.ts
│   │       │   └── constants.ts
│   │       └── index.ts                # 导出入口
│   │
│   └── ui/                             # UI组件库
│       ├── package.json
│       ├── tsconfig.json
│       └── src/
│           ├── components/             # 可复用UI组件
│           │   ├── Button/
│           │   │   ├── Button.tsx
│           │   │   ├── Button.test.tsx
│           │   │   └── index.ts
│           │   ├── Input/
│           │   ├── Modal/
│           │   └── Tooltip/
│           ├── hooks/                  # UI相关Hooks
│           ├── utils/                  # UI工具函数
│           └── index.ts                # 组件库入口
│
├── docs/                               # 项目文档
│   ├── brief.md                        # 项目简介
│   ├── prd.md                          # 产品需求文档
│   ├── architecture.md                 # 技术架构文档
│   ├── api/                            # API文档
│   ├── deployment/                     # 部署文档
│   └── development/                    # 开发文档
│       ├── setup.md                    # 开发环境设置
│       ├── coding-standards.md         # 编码规范
│       └── testing.md                  # 测试指南
│
├── .bmad-core/                         # BMad方法论配置
│   ├── agents/                         # 代理配置
│   ├── tasks/                          # 任务模板
│   ├── templates/                      # 文档模板
│   └── checklists/                     # 检查清单
│
├── scripts/                            # 构建和部署脚本
│   ├── build.js                        # 构建脚本
│   ├── dev.js                          # 开发脚本
│   ├── test.js                         # 测试脚本
│   └── deploy.js                       # 部署脚本
│
└── tools/                              # 开发工具
    ├── eslint.config.js                # ESLint配置
    ├── prettier.config.js              # Prettier配置
    ├── jest.config.js                  # Jest配置
    └── playwright.config.ts            # Playwright配置

### **Configuration Files (配置文件)**

#### **Root package.json**
```json
{
  "name": "refly-sqlite",
  "version": "1.0.0",
  "description": "Local-first card canvas application with AI integration",
  "private": true,
  "workspaces": [
    "apps/*",
    "packages/*"
  ],
  "scripts": {
    "dev": "npm run dev --workspace=apps/web",
    "build": "npm run build --workspace=apps/web",
    "test": "npm run test --workspaces",
    "test:e2e": "playwright test",
    "lint": "eslint . --ext .ts,.tsx",
    "lint:fix": "eslint . --ext .ts,.tsx --fix",
    "format": "prettier --write .",
    "type-check": "tsc --noEmit",
    "clean": "rm -rf node_modules apps/*/node_modules packages/*/node_modules",
    "postinstall": "npm run build --workspace=packages/shared && npm run build --workspace=packages/ui"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^6.15.0",
    "@typescript-eslint/parser": "^6.15.0",
    "eslint": "^8.56.0",
    "eslint-config-prettier": "^9.1.0",
    "eslint-plugin-react": "^7.33.2",
    "eslint-plugin-react-hooks": "^4.6.0",
    "prettier": "^3.1.1",
    "typescript": "^5.3.3",
    "@playwright/test": "^1.40.1"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=9.0.0"
  }
}
```

#### **Web App package.json**
```json
{
  "name": "@refly/web",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.20.1",
    "konva": "^9.2.0",
    "react-konva": "^18.2.10",
    "zustand": "^4.4.7",
    "marked": "^11.0.0",
    "highlight.js": "^11.9.0",
    "date-fns": "^3.0.0",
    "zod": "^3.22.4",
    "lucide-react": "^0.300.0",
    "alova": "^2.17.0",
    "sqlite3-wasm": "^3.44.0",
    "@headlessui/react": "^1.7.17",
    "@refly/shared": "*",
    "@refly/ui": "*"
  },
  "devDependencies": {
    "@types/react": "^18.2.43",
    "@types/react-dom": "^18.2.17",
    "@vitejs/plugin-react": "^4.2.1",
    "vite": "^5.0.8",
    "vitest": "^1.1.0",
    "@vitest/ui": "^1.1.0",
    "@vitest/coverage-v8": "^1.1.0",
    "@testing-library/react": "^14.1.2",
    "@testing-library/jest-dom": "^6.1.5",
    "tailwindcss": "^3.4.0",
    "autoprefixer": "^10.4.16",
    "postcss": "^8.4.32"
  }
}
```

#### **Vite Configuration**
```typescript
// apps/web/vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],

  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@shared': path.resolve(__dirname, '../../packages/shared/src'),
      '@ui': path.resolve(__dirname, '../../packages/ui/src')
    }
  },

  server: {
    port: 3000,
    host: true,
    headers: {
      // SQLite WASM需要的安全头
      'Cross-Origin-Embedder-Policy': 'require-corp',
      'Cross-Origin-Opener-Policy': 'same-origin'
    }
  },

  build: {
    target: 'esnext',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          canvas: ['konva', 'react-konva'],
          ai: ['alova'],
          database: ['sqlite3-wasm'],
          ui: ['@headlessui/react', 'lucide-react']
        }
      }
    }
  },

  optimizeDeps: {
    include: ['sqlite3-wasm'],
    exclude: ['@refly/shared', '@refly/ui']
  },

  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts']
  }
});
```

#### **TypeScript Configuration**
```json
// tsconfig.json (root)
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@shared/*": ["packages/shared/src/*"],
      "@ui/*": ["packages/ui/src/*"]
    }
  },
  "references": [
    { "path": "./apps/web" },
    { "path": "./packages/shared" },
    { "path": "./packages/ui" }
  ]
}
```

#### **TailwindCSS Configuration**
```javascript
// apps/web/tailwind.config.js
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "../../packages/ui/src/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {
      colors: {
        canvas: {
          bg: '#f8fafc',
          grid: '#e2e8f0'
        },
        card: {
          bg: '#ffffff',
          border: '#e5e7eb',
          shadow: 'rgba(0, 0, 0, 0.1)'
        },
        connection: {
          default: '#6b7280',
          active: '#3b82f6',
          hover: '#1d4ed8'
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace']
      },
      animation: {
        'fade-in': 'fadeIn 0.2s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-slow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite'
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' }
        }
      }
    }
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms')
  ]
};
```

---

## **Development Workflow (开发工作流)**

### **Local Development Setup (本地开发环境设置)**

#### **Prerequisites (前置要求)**
- Node.js 18.0+
- npm 9.0+
- Git
- 现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+)

#### **Initial Setup (初始设置)**
```bash
# 1. 克隆仓库
git clone <repository-url>
cd refly_sqlite

# 2. 安装依赖
npm install

# 3. 构建共享包
npm run build --workspace=packages/shared
npm run build --workspace=packages/ui

# 4. 启动开发服务器
npm run dev

# 5. 在浏览器中打开 http://localhost:3000
```

#### **Environment Configuration (环境配置)**
```bash
# .env.local (apps/web/)
VITE_APP_NAME="Card Canvas"
VITE_APP_VERSION="1.0.0"
VITE_OPENROUTER_BASE_URL="https://openrouter.ai/api/v1"
VITE_ENABLE_DEBUG=true
VITE_ENABLE_ANALYTICS=false
```

### **Development Tools (开发工具)**

#### **Code Quality Tools (代码质量工具)**
```javascript
// .eslintrc.js
module.exports = {
  root: true,
  env: {
    browser: true,
    es2022: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'prettier'
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true
    }
  },
  plugins: ['@typescript-eslint', 'react', 'react-hooks'],
  rules: {
    'react/react-in-jsx-scope': 'off',
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn'
  },
  settings: {
    react: {
      version: 'detect'
    }
  }
};
```

#### **Testing Configuration (测试配置)**
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*'
      ]
    }
  }
});

// src/test/setup.ts
import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock SQLite WASM
vi.mock('sqlite3-wasm', () => ({
  default: {
    Database: vi.fn(() => ({
      exec: vi.fn(),
      prepare: vi.fn(() => ({
        step: vi.fn(),
        getAsObject: vi.fn(),
        free: vi.fn(),
        run: vi.fn()
      })),
      close: vi.fn(),
      export: vi.fn(() => new Uint8Array())
    }))
  }
}));

// Mock Web Crypto API
Object.defineProperty(window, 'crypto', {
  value: {
    subtle: {
      generateKey: vi.fn(),
      importKey: vi.fn(),
      exportKey: vi.fn(),
      encrypt: vi.fn(),
      decrypt: vi.fn()
    },
    getRandomValues: vi.fn((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    })
  }
});
```

### **Development Best Practices (开发最佳实践)**

#### **Git Workflow (Git工作流)**
```bash
# 功能开发流程
git checkout -b feature/card-editing
# 开发功能...
git add .
git commit -m "feat: implement card editing functionality"
git push origin feature/card-editing
# 创建Pull Request

# 提交消息规范 (Conventional Commits)
feat: 新功能
fix: 错误修复
docs: 文档更新
style: 代码格式化
refactor: 代码重构
test: 测试相关
chore: 构建工具或辅助工具的变动
```

#### **Code Organization (代码组织)**
```typescript
// 组件文件结构示例
// components/canvas/CardComponent/
├── CardComponent.tsx        # 主组件
├── CardComponent.test.tsx   # 单元测试
├── CardComponent.stories.tsx # Storybook故事
├── hooks/                   # 组件专用hooks
│   └── useCardInteraction.ts
├── types.ts                 # 组件类型定义
└── index.ts                 # 导出入口

// 组件实现模式
export const CardComponent: React.FC<CardProps> = ({
  card,
  onUpdate,
  onDelete
}) => {
  // 1. Hooks调用
  const { isEditing, startEdit, stopEdit } = useCardInteraction();

  // 2. 事件处理函数
  const handleContentChange = useCallback((content: string) => {
    onUpdate(card.id, { content });
  }, [card.id, onUpdate]);

  // 3. 渲染逻辑
  return (
    <div className="card-component">
      {/* 组件内容 */}
    </div>
  );
};

---

## **Deployment Architecture (部署架构)**

### **Local Development Focus (本地开发重点)**

根据用户需求，本项目专注于本地开发环境，不涉及传统的生产部署。但为了完整性，提供基本的构建和分发指导。

#### **Local Development Server (本地开发服务器)**
```bash
# 开发模式启动
npm run dev

# 开发服务器配置
Server: Vite Dev Server
Port: 3000
Host: localhost (可配置为 0.0.0.0 支持局域网访问)
Hot Reload: 启用
Source Maps: 启用
```

#### **Build Configuration (构建配置)**
```bash
# 生产构建
npm run build

# 构建输出
Output Directory: apps/web/dist/
Assets: 静态资源文件
Chunks: 代码分割后的JS文件
Index: 入口HTML文件
```

#### **Local Distribution (本地分发)**
```bash
# 预览构建结果
npm run preview

# 静态文件服务
# 可以使用任何静态文件服务器托管dist目录
# 例如：
npx serve apps/web/dist
python -m http.server 8000 -d apps/web/dist
```

### **Browser Requirements (浏览器要求)**
- **Chrome 90+**: 完整支持，推荐使用
- **Firefox 88+**: 完整支持
- **Safari 14+**: 完整支持
- **Edge 90+**: 完整支持

### **Security Headers (安全头配置)**
```typescript
// 开发服务器安全头 (vite.config.ts)
server: {
  headers: {
    'Cross-Origin-Embedder-Policy': 'require-corp',
    'Cross-Origin-Opener-Policy': 'same-origin',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block'
  }
}
```

---

## **Security and Performance (安全和性能)**

### **Security Requirements (安全要求)**

#### **Data Security (数据安全)**
```typescript
// 1. 本地数据加密
class DataEncryption {
  // API密钥加密存储
  async encryptAPIKey(key: string): Promise<string> {
    const cryptoKey = await this.generateOrGetKey();
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const encoded = new TextEncoder().encode(key);

    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      cryptoKey,
      encoded
    );

    return this.combineIvAndData(iv, encrypted);
  }

  // 敏感数据清理
  clearSensitiveData(): void {
    // 清理内存中的敏感数据
    localStorage.removeItem('openrouter_api_key');
    sessionStorage.clear();
  }
}

// 2. 输入验证和清理
class InputValidator {
  static validateMarkdown(content: string): ValidationResult {
    // 防止XSS攻击
    const sanitized = DOMPurify.sanitize(content);

    // 长度限制
    if (content.length > 50000) {
      return { valid: false, error: '内容长度超出限制' };
    }

    // 恶意脚本检测
    if (this.containsMaliciousScript(content)) {
      return { valid: false, error: '检测到潜在的恶意内容' };
    }

    return { valid: true, sanitized };
  }

  static validateAPIKey(key: string): boolean {
    // API密钥格式验证
    return /^sk-[a-zA-Z0-9]{32,}$/.test(key);
  }
}
```

#### **API Security (API安全)**
```typescript
// OpenRouter API安全配置
class SecureAPIClient {
  private rateLimiter: RateLimiter;
  private requestValidator: RequestValidator;

  constructor() {
    this.rateLimiter = new RateLimiter({
      maxRequests: 60,
      windowMs: 60000 // 1分钟60次请求
    });

    this.requestValidator = new RequestValidator();
  }

  async makeRequest(request: APIRequest): Promise<APIResponse> {
    // 速率限制检查
    if (!this.rateLimiter.checkLimit()) {
      throw new Error('请求频率过高，请稍后重试');
    }

    // 请求验证
    this.requestValidator.validate(request);

    // 添加安全头
    const secureRequest = {
      ...request,
      headers: {
        ...request.headers,
        'User-Agent': 'CardCanvas/1.0.0',
        'X-Request-ID': generateRequestId()
      }
    };

    return this.executeRequest(secureRequest);
  }
}

// 速率限制器
class RateLimiter {
  private requests: number[] = [];

  constructor(private config: { maxRequests: number; windowMs: number }) {}

  checkLimit(): boolean {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;

    // 清理过期请求
    this.requests = this.requests.filter(time => time > windowStart);

    // 检查是否超出限制
    if (this.requests.length >= this.config.maxRequests) {
      return false;
    }

    // 记录当前请求
    this.requests.push(now);
    return true;
  }
}
```

### **Performance Optimization (性能优化)**

#### **Memory Management (内存管理)**
```typescript
// LRU缓存实现
class LRUCache<T> {
  private cache = new Map<string, { value: T; timestamp: number }>();
  private maxSize: number;
  private ttl: number;

  constructor(maxSize = 100, ttl = 300000) { // 5分钟TTL
    this.maxSize = maxSize;
    this.ttl = ttl;
  }

  set(key: string, value: T): void {
    // 清理过期项
    this.cleanup();

    // 如果缓存已满，删除最旧的项
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now()
    });
  }

  get(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;

    // 检查是否过期
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }

    // 更新访问时间（LRU）
    this.cache.delete(key);
    this.cache.set(key, item);

    return item.value;
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > this.ttl) {
        this.cache.delete(key);
      }
    }
  }

  getStats(): CacheStats {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.calculateHitRate(),
      missRate: this.calculateMissRate(),
      evictions: this.evictionCount
    };
  }
}

// 内存监控
class MemoryMonitor {
  private memoryUsage: number[] = [];

  startMonitoring(): void {
    setInterval(() => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        this.memoryUsage.push({
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit,
          timestamp: Date.now()
        });

        // 保留最近100个记录
        if (this.memoryUsage.length > 100) {
          this.memoryUsage.shift();
        }

        // 内存警告
        if (memory.usedJSHeapSize / memory.jsHeapSizeLimit > 0.8) {
          console.warn('内存使用率过高，建议清理缓存');
          this.triggerCleanup();
        }
      }
    }, 5000); // 每5秒检查一次
  }

  private triggerCleanup(): void {
    // 触发垃圾回收建议
    if ('gc' in window && typeof window.gc === 'function') {
      window.gc();
    }

    // 清理应用缓存
    serviceContainer.get('cache').cleanup();
  }
}
```

#### **Canvas Performance (画布性能)**
```typescript
// 画布性能优化
class CanvasPerformanceOptimizer {
  private visibleCards = new Set<string>();
  private renderQueue: RenderTask[] = [];
  private isRendering = false;

  // 视口裁剪 - 只渲染可见区域的卡片
  updateVisibleCards(viewport: Viewport, cards: Card[]): void {
    const newVisibleCards = new Set<string>();

    for (const card of cards) {
      if (this.isCardVisible(card, viewport)) {
        newVisibleCards.add(card.id);
      }
    }

    // 找出需要添加和移除的卡片
    const toAdd = [...newVisibleCards].filter(id => !this.visibleCards.has(id));
    const toRemove = [...this.visibleCards].filter(id => !newVisibleCards.has(id));

    // 批量更新
    this.batchRenderUpdate(toAdd, toRemove);
    this.visibleCards = newVisibleCards;
  }

  private isCardVisible(card: Card, viewport: Viewport): boolean {
    const cardBounds = {
      left: card.position_x,
      top: card.position_y,
      right: card.position_x + card.width,
      bottom: card.position_y + card.height
    };

    const viewBounds = {
      left: viewport.x,
      top: viewport.y,
      right: viewport.x + (window.innerWidth / viewport.zoom),
      bottom: viewport.y + (window.innerHeight / viewport.zoom)
    };

    return !(cardBounds.right < viewBounds.left ||
             cardBounds.left > viewBounds.right ||
             cardBounds.bottom < viewBounds.top ||
             cardBounds.top > viewBounds.bottom);
  }

  // 批量渲染更新
  private batchRenderUpdate(toAdd: string[], toRemove: string[]): void {
    this.renderQueue.push({
      type: 'batch',
      add: toAdd,
      remove: toRemove,
      timestamp: Date.now()
    });

    this.scheduleRender();
  }

  private scheduleRender(): void {
    if (this.isRendering) return;

    requestAnimationFrame(() => {
      this.isRendering = true;
      this.processRenderQueue();
      this.isRendering = false;
    });
  }

  private processRenderQueue(): void {
    while (this.renderQueue.length > 0) {
      const task = this.renderQueue.shift()!;
      this.executeRenderTask(task);
    }
  }
}

// 虚拟化列表（用于大量卡片时）
class VirtualizedCardList {
  private itemHeight = 150;
  private containerHeight = 600;
  private scrollTop = 0;

  getVisibleRange(totalItems: number): { start: number; end: number } {
    const visibleCount = Math.ceil(this.containerHeight / this.itemHeight);
    const start = Math.floor(this.scrollTop / this.itemHeight);
    const end = Math.min(start + visibleCount + 1, totalItems);

    return { start: Math.max(0, start - 1), end };
  }

  updateScroll(scrollTop: number): void {
    this.scrollTop = scrollTop;
  }
}

---

## **Testing Strategy (测试策略)**

### **Testing Pyramid (测试金字塔)**

#### **Unit Tests (单元测试) - 70%**
```typescript
// 组件单元测试示例
// CardComponent.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { CardComponent } from './CardComponent';
import { mockCard } from '@/test/mocks';

describe('CardComponent', () => {
  const defaultProps = {
    card: mockCard,
    onUpdate: vi.fn(),
    onDelete: vi.fn(),
    onSelect: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders card with correct content', () => {
    render(<CardComponent {...defaultProps} />);

    expect(screen.getByText(mockCard.title)).toBeInTheDocument();
    expect(screen.getByText(mockCard.content)).toBeInTheDocument();
  });

  it('enters edit mode on double click', () => {
    render(<CardComponent {...defaultProps} />);

    const cardElement = screen.getByTestId('card-content');
    fireEvent.doubleClick(cardElement);

    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('calls onUpdate when content changes', async () => {
    render(<CardComponent {...defaultProps} />);

    const cardElement = screen.getByTestId('card-content');
    fireEvent.doubleClick(cardElement);

    const textbox = screen.getByRole('textbox');
    fireEvent.change(textbox, { target: { value: 'New content' } });
    fireEvent.blur(textbox);

    expect(defaultProps.onUpdate).toHaveBeenCalledWith(
      mockCard.id,
      { content: 'New content' }
    );
  });
});

// 服务层单元测试示例
// cardService.test.ts
import { CardService } from './cardService';
import { mockDatabase, mockCache } from '@/test/mocks';

describe('CardService', () => {
  let cardService: CardService;

  beforeEach(() => {
    cardService = new CardService(mockDatabase, mockCache);
  });

  describe('createCard', () => {
    it('creates card with valid data', async () => {
      const cardData = {
        canvas_id: 'canvas-1',
        title: 'Test Card',
        content: 'Test content',
        position_x: 100,
        position_y: 200
      };

      const result = await cardService.createCard(cardData);

      expect(result).toMatchObject({
        ...cardData,
        id: expect.any(String),
        created_at: expect.any(Date),
        updated_at: expect.any(Date)
      });

      expect(mockDatabase.executeNonQuery).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO cards'),
        expect.any(Array)
      );
    });

    it('throws error for invalid canvas_id', async () => {
      const invalidData = {
        canvas_id: '',
        title: 'Test Card',
        content: 'Test content',
        position_x: 100,
        position_y: 200
      };

      await expect(cardService.createCard(invalidData))
        .rejects.toThrow('Invalid canvas_id');
    });
  });
});
```

#### **Integration Tests (集成测试) - 20%**
```typescript
// 数据库集成测试
// database.integration.test.ts
import { DatabaseManager } from '@/services/database';
import { CanvasService } from '@/services/canvas';
import { CardService } from '@/services/card';

describe('Database Integration', () => {
  let db: DatabaseManager;
  let canvasService: CanvasService;
  let cardService: CardService;

  beforeAll(async () => {
    db = new DatabaseManager();
    await db.initialize();

    canvasService = new CanvasService(db, mockCache);
    cardService = new CardService(db, mockCache);
  });

  afterAll(async () => {
    await db.close();
  });

  beforeEach(async () => {
    // 清理数据库
    await db.executeNonQuery('DELETE FROM cards');
    await db.executeNonQuery('DELETE FROM canvases');
  });

  it('creates canvas and cards with proper relationships', async () => {
    // 创建画布
    const canvas = await canvasService.createCanvas({
      name: 'Test Canvas',
      description: 'Integration test canvas'
    });

    // 创建卡片
    const card1 = await cardService.createCard({
      canvas_id: canvas.id,
      title: 'Card 1',
      content: 'Content 1',
      position_x: 100,
      position_y: 100
    });

    const card2 = await cardService.createCard({
      canvas_id: canvas.id,
      title: 'Card 2',
      content: 'Content 2',
      position_x: 200,
      position_y: 200
    });

    // 验证关系
    const canvasCards = await cardService.getCardsByCanvas(canvas.id);
    expect(canvasCards).toHaveLength(2);
    expect(canvasCards.map(c => c.id)).toContain(card1.id);
    expect(canvasCards.map(c => c.id)).toContain(card2.id);
  });

  it('handles cascade deletion correctly', async () => {
    const canvas = await canvasService.createCanvas({
      name: 'Test Canvas',
      description: 'Test'
    });

    await cardService.createCard({
      canvas_id: canvas.id,
      title: 'Test Card',
      content: 'Test',
      position_x: 0,
      position_y: 0
    });

    // 删除画布应该级联删除卡片
    await canvasService.deleteCanvas(canvas.id);

    const remainingCards = await cardService.getCardsByCanvas(canvas.id);
    expect(remainingCards).toHaveLength(0);
  });
});

// AI服务集成测试
// aiService.integration.test.ts
import { AIIntegrationService } from '@/services/ai';
import { mockOpenRouterClient } from '@/test/mocks';

describe('AI Service Integration', () => {
  let aiService: AIIntegrationService;

  beforeEach(() => {
    aiService = new AIIntegrationService(mockDatabase, mockEncryption);
    // 注入mock客户端
    (aiService as any).client = mockOpenRouterClient;
  });

  it('formats cards as context correctly', async () => {
    const cards = [
      { id: '1', title: 'Card 1', content: '# Heading\nContent 1', tags: ['tag1'] },
      { id: '2', title: 'Card 2', content: 'Content 2', tags: [] }
    ];

    const context = aiService.formatCardsAsContext(cards);

    expect(context).toContain('# 卡片上下文');
    expect(context).toContain('## Card 1');
    expect(context).toContain('# Heading\nContent 1');
    expect(context).toContain('标签: tag1');
    expect(context).toContain('## Card 2');
    expect(context).toContain('Content 2');
  });

  it('handles conversation flow correctly', async () => {
    const conversation = await aiService.createConversation('canvas-1', ['card-1']);

    mockOpenRouterClient.createChatCompletion.mockResolvedValue({
      choices: [{ message: { content: 'AI response' } }],
      usage: { total_tokens: 100 }
    });

    const response = await aiService.sendMessage(conversation.id, 'Test message');

    expect(response.content).toBe('AI response');
    expect(mockDatabase.executeNonQuery).toHaveBeenCalledWith(
      expect.stringContaining('INSERT INTO ai_messages'),
      expect.any(Array)
    );
  });
});
```

#### **End-to-End Tests (端到端测试) - 10%**
```typescript
// e2e/canvas.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Canvas Operations', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    // 等待应用加载
    await page.waitForSelector('[data-testid="canvas-container"]');
  });

  test('creates and edits cards', async ({ page }) => {
    // 创建新卡片
    await page.click('[data-testid="add-card-button"]');

    // 验证卡片已创建
    const card = page.locator('[data-testid="card"]').first();
    await expect(card).toBeVisible();

    // 编辑卡片内容
    await card.dblclick();
    const editor = page.locator('[data-testid="card-editor"]');
    await expect(editor).toBeVisible();

    await editor.fill('# Test Card\nThis is test content');
    await page.keyboard.press('Escape');

    // 验证内容已保存
    await expect(card.locator('h1')).toHaveText('Test Card');
    await expect(card).toContainText('This is test content');
  });

  test('creates connections between cards', async ({ page }) => {
    // 创建两个卡片
    await page.click('[data-testid="add-card-button"]');
    await page.click('[data-testid="add-card-button"]');

    const cards = page.locator('[data-testid="card"]');
    await expect(cards).toHaveCount(2);

    // 创建连接
    const firstCard = cards.first();
    const secondCard = cards.last();

    await firstCard.locator('[data-testid="connection-button"]').click();
    await secondCard.click();

    // 验证连接已创建
    const connection = page.locator('[data-testid="connection-line"]');
    await expect(connection).toBeVisible();
  });

  test('integrates with AI chat', async ({ page }) => {
    // 创建卡片并添加内容
    await page.click('[data-testid="add-card-button"]');
    const card = page.locator('[data-testid="card"]').first();

    await card.dblclick();
    await page.locator('[data-testid="card-editor"]').fill('Test card content');
    await page.keyboard.press('Escape');

    // 选择卡片并开始AI对话
    await card.click();
    await page.click('[data-testid="chat-with-ai-button"]');

    // 验证聊天面板打开
    const chatPanel = page.locator('[data-testid="chat-panel"]');
    await expect(chatPanel).toBeVisible();

    // 发送消息
    const messageInput = page.locator('[data-testid="message-input"]');
    await messageInput.fill('Analyze this card content');
    await page.click('[data-testid="send-message-button"]');

    // 验证消息已发送
    await expect(page.locator('[data-testid="user-message"]')).toContainText('Analyze this card content');

    // 等待AI响应（在测试环境中使用mock响应）
    await expect(page.locator('[data-testid="ai-message"]')).toBeVisible({ timeout: 5000 });
  });

  test('handles canvas navigation and zoom', async ({ page }) => {
    // 测试缩放功能
    await page.click('[data-testid="zoom-in-button"]');
    await page.click('[data-testid="zoom-in-button"]');

    // 验证缩放级别
    const zoomLevel = page.locator('[data-testid="zoom-level"]');
    await expect(zoomLevel).toContainText('150%');

    // 测试平移功能
    const canvas = page.locator('[data-testid="canvas-container"]');
    await canvas.dragTo(canvas, {
      sourcePosition: { x: 100, y: 100 },
      targetPosition: { x: 200, y: 200 }
    });

    // 验证视口已移动
    const viewport = await page.evaluate(() => {
      return window.canvasEngine?.getViewport();
    });
    expect(viewport.x).not.toBe(0);
    expect(viewport.y).not.toBe(0);
  });
});

// e2e/performance.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Performance Tests', () => {
  test('handles large number of cards efficiently', async ({ page }) => {
    await page.goto('/');

    // 创建大量卡片
    for (let i = 0; i < 100; i++) {
      await page.click('[data-testid="add-card-button"]');
    }

    // 测试渲染性能
    const startTime = Date.now();
    await page.locator('[data-testid="card"]').first().waitFor();
    const renderTime = Date.now() - startTime;

    expect(renderTime).toBeLessThan(2000); // 2秒内完成渲染

    // 测试滚动性能
    await page.evaluate(() => {
      window.scrollTo(0, 1000);
    });

    // 验证虚拟化工作正常
    const visibleCards = await page.locator('[data-testid="card"]:visible').count();
    expect(visibleCards).toBeLessThan(50); // 只渲染可见卡片
  });

  test('memory usage remains stable', async ({ page }) => {
    await page.goto('/');

    // 获取初始内存使用
    const initialMemory = await page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0;
    });

    // 执行大量操作
    for (let i = 0; i < 50; i++) {
      await page.click('[data-testid="add-card-button"]');
      await page.locator('[data-testid="card"]').last().dblclick();
      await page.locator('[data-testid="card-editor"]').fill(`Card ${i} content`);
      await page.keyboard.press('Escape');
    }

    // 检查内存使用
    const finalMemory = await page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0;
    });

    const memoryIncrease = finalMemory - initialMemory;
    expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024); // 内存增长小于50MB
  });
});
```

### **Test Data Management (测试数据管理)**
```typescript
// test/mocks/index.ts
export const mockCard: Card = {
  id: 'card-1',
  canvas_id: 'canvas-1',
  title: 'Test Card',
  content: '# Test Card\nThis is test content',
  position_x: 100,
  position_y: 200,
  width: 200,
  height: 150,
  z_index: 0,
  style: {
    backgroundColor: '#ffffff',
    borderColor: '#e5e7eb',
    textColor: '#111827',
    fontSize: 14
  },
  tags: ['test', 'mock'],
  created_at: new Date('2024-01-01'),
  updated_at: new Date('2024-01-01')
};

export const mockCanvas: Canvas = {
  id: 'canvas-1',
  name: 'Test Canvas',
  description: 'Test canvas for unit tests',
  theme: CanvasTheme.LIGHT,
  viewport: { x: 0, y: 0, zoom: 1 },
  settings: {
    gridEnabled: true,
    snapToGrid: true,
    gridSize: 20,
    backgroundColor: '#ffffff'
  },
  created_at: new Date('2024-01-01'),
  updated_at: new Date('2024-01-01')
};

// 数据库Mock
export const mockDatabase = {
  executeQuery: vi.fn(),
  executeNonQuery: vi.fn(),
  transaction: vi.fn(),
  initialize: vi.fn(),
  close: vi.fn()
};

// 缓存Mock
export const mockCache = {
  get: vi.fn(),
  set: vi.fn(),
  has: vi.fn(),
  delete: vi.fn(),
  clear: vi.fn()
};
```

---

## **Coding Standards (编码标准)**

### **TypeScript Standards (TypeScript标准)**

#### **Type Definitions (类型定义)**
```typescript
// 1. 接口命名使用PascalCase
interface CardProps {
  card: Card;
  onUpdate: (id: string, updates: Partial<Card>) => void;
  onDelete: (id: string) => void;
  isSelected?: boolean;
}

// 2. 类型别名使用PascalCase
type CardEventHandler = (cardId: string, event: CardEvent) => void;
type DatabaseConnection = SQLiteDatabase | null;

// 3. 枚举使用PascalCase，值使用UPPER_SNAKE_CASE
enum ConnectionType {
  ARROW = 'arrow',
  LINE = 'line',
  CURVE = 'curve'
}

// 4. 泛型参数使用单个大写字母
interface Repository<T> {
  findById(id: string): Promise<T | null>;
  save(entity: T): Promise<T>;
  delete(id: string): Promise<void>;
}

// 5. 联合类型使用描述性名称
type Theme = 'light' | 'dark' | 'auto';
type CardSize = 'small' | 'medium' | 'large';

// 6. 严格的null检查
function processCard(card: Card | null): string {
  if (!card) {
    throw new Error('Card is required');
  }
  return card.title;
}

// 7. 使用readonly修饰符保护数据
interface ReadonlyCard {
  readonly id: string;
  readonly created_at: Date;
  readonly canvas_id: string;
}
```

#### **Function and Class Standards (函数和类标准)**
```typescript
// 1. 函数命名使用camelCase，描述性动词
function createCard(data: CreateCardRequest): Promise<Card> {
  return cardService.create(data);
}

function validateCardContent(content: string): ValidationResult {
  // 验证逻辑
}

// 2. 类命名使用PascalCase
class CardService {
  private database: DatabaseManager;
  private cache: CacheManager;

  constructor(database: DatabaseManager, cache: CacheManager) {
    this.database = database;
    this.cache = cache;
  }

  // 3. 公共方法使用camelCase
  async createCard(data: CreateCardRequest): Promise<Card> {
    this.validateCreateRequest(data);
    return this.executeCreate(data);
  }

  // 4. 私有方法使用camelCase，明确标记private
  private validateCreateRequest(data: CreateCardRequest): void {
    if (!data.canvas_id) {
      throw new Error('Canvas ID is required');
    }
  }

  private async executeCreate(data: CreateCardRequest): Promise<Card> {
    // 实现逻辑
  }
}

// 5. 常量使用UPPER_SNAKE_CASE
const MAX_CARD_CONTENT_LENGTH = 50000;
const DEFAULT_CARD_SIZE = { width: 200, height: 150 };
const API_ENDPOINTS = {
  CHAT_COMPLETIONS: '/chat/completions',
  MODELS: '/models'
} as const;

// 6. 错误处理模式
class CardError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly cardId?: string
  ) {
    super(message);
    this.name = 'CardError';
  }
}

function handleCardOperation<T>(operation: () => Promise<T>): Promise<T> {
  return operation().catch(error => {
    if (error instanceof CardError) {
      console.error(`Card operation failed: ${error.message}`, {
        code: error.code,
        cardId: error.cardId
      });
    }
    throw error;
  });
}

### **React Component Standards (React组件标准)**

#### **Component Structure (组件结构)**
```typescript
// 1. 组件文件结构
// CardComponent/
// ├── CardComponent.tsx
// ├── CardComponent.test.tsx
// ├── hooks/
// │   └── useCardInteraction.ts
// ├── types.ts
// └── index.ts

// 2. 组件定义模式
interface CardComponentProps {
  card: Card;
  isSelected?: boolean;
  onUpdate: (id: string, updates: Partial<Card>) => void;
  onDelete: (id: string) => void;
  onSelect: (id: string) => void;
}

export const CardComponent: React.FC<CardComponentProps> = ({
  card,
  isSelected = false,
  onUpdate,
  onDelete,
  onSelect
}) => {
  // 1. Hooks调用（按依赖顺序）
  const [isEditing, setIsEditing] = useState(false);
  const [content, setContent] = useState(card.content);
  const { isHovered, handleMouseEnter, handleMouseLeave } = useCardInteraction();

  // 2. 计算值
  const cardStyle = useMemo(() => ({
    transform: `translate(${card.position_x}px, ${card.position_y}px)`,
    width: card.width,
    height: card.height,
    zIndex: card.z_index
  }), [card.position_x, card.position_y, card.width, card.height, card.z_index]);

  // 3. 事件处理函数
  const handleDoubleClick = useCallback(() => {
    setIsEditing(true);
  }, []);

  const handleContentSave = useCallback(() => {
    if (content !== card.content) {
      onUpdate(card.id, { content });
    }
    setIsEditing(false);
  }, [card.id, card.content, content, onUpdate]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setContent(card.content); // 重置内容
      setIsEditing(false);
    } else if (e.key === 'Enter' && e.ctrlKey) {
      handleContentSave();
    }
  }, [card.content, handleContentSave]);

  // 4. 副作用
  useEffect(() => {
    setContent(card.content);
  }, [card.content]);

  // 5. 渲染逻辑
  return (
    <div
      className={cn(
        'absolute bg-white border rounded-lg shadow-sm cursor-pointer transition-all',
        isSelected && 'ring-2 ring-blue-500',
        isHovered && 'shadow-md'
      )}
      style={cardStyle}
      onClick={() => onSelect(card.id)}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      data-testid="card"
    >
      {isEditing ? (
        <CardEditor
          content={content}
          onChange={setContent}
          onSave={handleContentSave}
          onKeyDown={handleKeyDown}
        />
      ) : (
        <CardContent card={card} />
      )}
    </div>
  );
};

// 3. 子组件分离
const CardEditor: React.FC<{
  content: string;
  onChange: (content: string) => void;
  onSave: () => void;
  onKeyDown: (e: React.KeyboardEvent) => void;
}> = ({ content, onChange, onSave, onKeyDown }) => (
  <textarea
    className="w-full h-full p-2 border-none outline-none resize-none"
    value={content}
    onChange={(e) => onChange(e.target.value)}
    onBlur={onSave}
    onKeyDown={onKeyDown}
    autoFocus
    data-testid="card-editor"
  />
);

const CardContent: React.FC<{ card: Card }> = ({ card }) => (
  <div className="p-3 h-full overflow-hidden">
    <MarkdownRenderer content={card.content} />
  </div>
);
```

#### **Custom Hooks Standards (自定义Hooks标准)**
```typescript
// 1. Hook命名以use开头
// 2. 返回对象而非数组（除非顺序很重要）
// 3. 提供清晰的类型定义

interface UseCardInteractionReturn {
  isHovered: boolean;
  isDragging: boolean;
  handleMouseEnter: () => void;
  handleMouseLeave: () => void;
  handleDragStart: (e: React.DragEvent) => void;
  handleDragEnd: () => void;
}

export const useCardInteraction = (): UseCardInteractionReturn => {
  const [isHovered, setIsHovered] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  const handleDragStart = useCallback((e: React.DragEvent) => {
    setIsDragging(true);
    e.dataTransfer.effectAllowed = 'move';
  }, []);

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  return {
    isHovered,
    isDragging,
    handleMouseEnter,
    handleMouseLeave,
    handleDragStart,
    handleDragEnd
  };
};

// 复杂状态管理Hook
interface UseCanvasStateReturn {
  canvas: Canvas | null;
  cards: Card[];
  selectedCards: string[];
  actions: {
    loadCanvas: (id: string) => Promise<void>;
    createCard: (data: CreateCardRequest) => Promise<Card>;
    updateCard: (id: string, updates: Partial<Card>) => Promise<void>;
    deleteCard: (id: string) => Promise<void>;
    selectCards: (cardIds: string[]) => void;
  };
}

export const useCanvasState = (): UseCanvasStateReturn => {
  const canvasStore = useCanvasStore();
  const cardStore = useCardStore();

  const actions = useMemo(() => ({
    loadCanvas: async (id: string) => {
      const canvas = await canvasService.getCanvas(id);
      const cards = await cardService.getCardsByCanvas(id);

      canvasStore.setCurrentCanvas(canvas);
      cardStore.setCards(cards);
    },

    createCard: async (data: CreateCardRequest) => {
      const card = await cardService.createCard(data);
      cardStore.addCard(card);
      return card;
    },

    updateCard: async (id: string, updates: Partial<Card>) => {
      await cardService.updateCard(id, updates);
      cardStore.updateCard(id, updates);
    },

    deleteCard: async (id: string) => {
      await cardService.deleteCard(id);
      cardStore.deleteCard(id);
    },

    selectCards: (cardIds: string[]) => {
      cardStore.setSelectedCards(cardIds);
    }
  }), [canvasStore, cardStore]);

  return {
    canvas: canvasStore.currentCanvas,
    cards: cardStore.getCardsByCanvas(canvasStore.currentCanvas?.id || ''),
    selectedCards: cardStore.selectedCards,
    actions
  };
};
```

### **Service Layer Standards (服务层标准)**

#### **Service Implementation Pattern (服务实现模式)**
```typescript
// 1. 服务接口定义
interface ICardService {
  createCard(data: CreateCardRequest): Promise<Card>;
  getCard(id: string): Promise<Card>;
  updateCard(id: string, updates: Partial<Card>): Promise<Card>;
  deleteCard(id: string): Promise<void>;
  getCardsByCanvas(canvasId: string): Promise<Card[]>;
  searchCards(query: string, canvasId?: string): Promise<Card[]>;
}

// 2. 服务实现
export class CardService implements ICardService {
  constructor(
    private database: DatabaseManager,
    private cache: CacheManager,
    private validator: CardValidator = new CardValidator()
  ) {}

  async createCard(data: CreateCardRequest): Promise<Card> {
    // 1. 输入验证
    const validationResult = this.validator.validateCreateRequest(data);
    if (!validationResult.isValid) {
      throw new CardError(validationResult.error, 'VALIDATION_ERROR');
    }

    // 2. 业务逻辑
    const card: Card = {
      id: generateUUID(),
      ...data,
      title: this.extractTitle(data.content),
      z_index: await this.getNextZIndex(data.canvas_id),
      style: { ...DEFAULT_CARD_STYLE, ...data.style },
      tags: this.extractTags(data.content),
      created_at: new Date(),
      updated_at: new Date()
    };

    // 3. 数据持久化
    await this.database.transaction(async (tx) => {
      await tx.executeNonQuery(INSERT_CARD_SQL, [
        card.id, card.canvas_id, card.title, card.content,
        card.position_x, card.position_y, card.width, card.height,
        card.z_index, JSON.stringify(card.style), JSON.stringify(card.tags),
        card.created_at.toISOString(), card.updated_at.toISOString()
      ]);
    });

    // 4. 缓存更新
    this.cache.set(`card:${card.id}`, card);
    this.cache.invalidate(`canvas:${card.canvas_id}:cards`);

    // 5. 事件发布
    eventBus.emit('card:created', card);

    return card;
  }

  async updateCard(id: string, updates: Partial<Card>): Promise<Card> {
    // 1. 获取现有卡片
    const existingCard = await this.getCard(id);

    // 2. 验证更新
    const validationResult = this.validator.validateUpdateRequest(updates);
    if (!validationResult.isValid) {
      throw new CardError(validationResult.error, 'VALIDATION_ERROR', id);
    }

    // 3. 合并更新
    const updatedCard: Card = {
      ...existingCard,
      ...updates,
      title: updates.content ? this.extractTitle(updates.content) : existingCard.title,
      tags: updates.content ? this.extractTags(updates.content) : existingCard.tags,
      updated_at: new Date()
    };

    // 4. 持久化
    await this.database.executeNonQuery(UPDATE_CARD_SQL, [
      updatedCard.title, updatedCard.content, updatedCard.position_x,
      updatedCard.position_y, updatedCard.width, updatedCard.height,
      updatedCard.z_index, JSON.stringify(updatedCard.style),
      JSON.stringify(updatedCard.tags), updatedCard.updated_at.toISOString(),
      id
    ]);

    // 5. 缓存更新
    this.cache.set(`card:${id}`, updatedCard);
    this.cache.invalidate(`canvas:${updatedCard.canvas_id}:cards`);

    // 6. 事件发布
    eventBus.emit('card:updated', { id, updates });

    return updatedCard;
  }

  // 私有辅助方法
  private extractTitle(content: string): string {
    const lines = content.split('\n');
    const firstLine = lines[0]?.trim() || '';

    // 提取Markdown标题
    const titleMatch = firstLine.match(/^#+\s*(.+)$/);
    if (titleMatch) {
      return titleMatch[1].trim();
    }

    // 使用第一行作为标题（限制长度）
    return firstLine.substring(0, 50) || '无标题';
  }

  private extractTags(content: string): string[] {
    const tagRegex = /#(\w+)/g;
    const tags: string[] = [];
    let match;

    while ((match = tagRegex.exec(content)) !== null) {
      tags.push(match[1]);
    }

    return [...new Set(tags)]; // 去重
  }

  private async getNextZIndex(canvasId: string): Promise<number> {
    const result = await this.database.executeQuery<{ max_z: number }>(
      'SELECT MAX(z_index) as max_z FROM cards WHERE canvas_id = ?',
      [canvasId]
    );

    return (result[0]?.max_z || 0) + 1;
  }
}

// 3. 验证器
class CardValidator {
  validateCreateRequest(data: CreateCardRequest): ValidationResult {
    if (!data.canvas_id) {
      return { isValid: false, error: 'Canvas ID is required' };
    }

    if (data.content && data.content.length > MAX_CARD_CONTENT_LENGTH) {
      return { isValid: false, error: 'Content exceeds maximum length' };
    }

    if (data.position_x < -10000 || data.position_x > 10000) {
      return { isValid: false, error: 'X position out of bounds' };
    }

    if (data.position_y < -10000 || data.position_y > 10000) {
      return { isValid: false, error: 'Y position out of bounds' };
    }

    return { isValid: true };
  }

  validateUpdateRequest(updates: Partial<Card>): ValidationResult {
    if (updates.content && updates.content.length > MAX_CARD_CONTENT_LENGTH) {
      return { isValid: false, error: 'Content exceeds maximum length' };
    }

    if (updates.width && (updates.width < 50 || updates.width > 1000)) {
      return { isValid: false, error: 'Width out of bounds' };
    }

    if (updates.height && (updates.height < 30 || updates.height > 1000)) {
      return { isValid: false, error: 'Height out of bounds' };
    }

    return { isValid: true };
  }
}
```

### **Database Operations Standards (数据库操作标准)**

#### **SQL Query Organization (SQL查询组织)**
```typescript
// 1. SQL常量定义
const SQL_QUERIES = {
  // 卡片查询
  INSERT_CARD: `
    INSERT INTO cards (
      id, canvas_id, title, content, position_x, position_y,
      width, height, z_index, style_background_color, style_border_color,
      style_text_color, style_font_size, tags, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `,

  UPDATE_CARD: `
    UPDATE cards SET
      title = ?, content = ?, position_x = ?, position_y = ?,
      width = ?, height = ?, z_index = ?, style_background_color = ?,
      style_border_color = ?, style_text_color = ?, style_font_size = ?,
      tags = ?, updated_at = ?
    WHERE id = ?
  `,

  SELECT_CARD_BY_ID: `
    SELECT * FROM cards WHERE id = ?
  `,

  SELECT_CARDS_BY_CANVAS: `
    SELECT * FROM cards WHERE canvas_id = ? ORDER BY z_index ASC
  `,

  SEARCH_CARDS: `
    SELECT c.* FROM cards c
    JOIN cards_fts fts ON c.rowid = fts.rowid
    WHERE cards_fts MATCH ?
    ORDER BY rank
  `,

  DELETE_CARD: `
    DELETE FROM cards WHERE id = ?
  `
} as const;

// 2. 查询构建器模式
class QueryBuilder {
  private query = '';
  private params: any[] = [];

  select(columns: string[]): this {
    this.query = `SELECT ${columns.join(', ')}`;
    return this;
  }

  from(table: string): this {
    this.query += ` FROM ${table}`;
    return this;
  }

  where(condition: string, ...params: any[]): this {
    this.query += ` WHERE ${condition}`;
    this.params.push(...params);
    return this;
  }

  orderBy(column: string, direction: 'ASC' | 'DESC' = 'ASC'): this {
    this.query += ` ORDER BY ${column} ${direction}`;
    return this;
  }

  limit(count: number): this {
    this.query += ` LIMIT ${count}`;
    return this;
  }

  build(): { sql: string; params: any[] } {
    return { sql: this.query, params: this.params };
  }
}

// 使用示例
const { sql, params } = new QueryBuilder()
  .select(['*'])
  .from('cards')
  .where('canvas_id = ?', canvasId)
  .where('created_at > ?', startDate)
  .orderBy('updated_at', 'DESC')
  .limit(50)
  .build();
```

### **Error Handling Standards (错误处理标准)**

#### **Error Classification (错误分类)**
```typescript
// 1. 错误基类
abstract class AppError extends Error {
  abstract readonly code: string;
  abstract readonly statusCode: number;

  constructor(message: string, public readonly context?: Record<string, any>) {
    super(message);
    this.name = this.constructor.name;
  }
}

// 2. 具体错误类型
class ValidationError extends AppError {
  readonly code = 'VALIDATION_ERROR';
  readonly statusCode = 400;
}

class NotFoundError extends AppError {
  readonly code = 'NOT_FOUND';
  readonly statusCode = 404;
}

class DatabaseError extends AppError {
  readonly code = 'DATABASE_ERROR';
  readonly statusCode = 500;
}

class APIError extends AppError {
  readonly code = 'API_ERROR';
  readonly statusCode = 502;

  constructor(message: string, public readonly apiResponse?: any) {
    super(message);
  }
}

// 3. 错误处理器
class ErrorHandler {
  static handle(error: Error, context?: string): void {
    if (error instanceof AppError) {
      this.handleAppError(error, context);
    } else {
      this.handleUnknownError(error, context);
    }
  }

  private static handleAppError(error: AppError, context?: string): void {
    console.error(`[${error.code}] ${error.message}`, {
      context,
      details: error.context,
      stack: error.stack
    });

    // 发送到错误监控服务
    this.reportError(error, context);
  }

  private static handleUnknownError(error: Error, context?: string): void {
    console.error(`[UNKNOWN_ERROR] ${error.message}`, {
      context,
      stack: error.stack
    });

    this.reportError(error, context);
  }

  private static reportError(error: Error, context?: string): void {
    // 在生产环境中发送到错误监控服务
    if (process.env.NODE_ENV === 'production') {
      // 发送错误报告
    }
  }
}

// 4. React错误边界
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    ErrorHandler.handle(error, 'React Component');
    console.error('React Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>We're sorry, but something unexpected happened.</p>
          <button onClick={() => this.setState({ hasError: false })}>
            Try again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### **Performance Optimization Standards (性能优化标准)**

#### **React Performance Patterns (React性能模式)**
```typescript
// 1. 组件记忆化
const CardComponent = React.memo<CardComponentProps>(({ card, onUpdate, onDelete }) => {
  // 组件实现
}, (prevProps, nextProps) => {
  // 自定义比较函数
  return (
    prevProps.card.id === nextProps.card.id &&
    prevProps.card.updated_at.getTime() === nextProps.card.updated_at.getTime() &&
    prevProps.onUpdate === nextProps.onUpdate &&
    prevProps.onDelete === nextProps.onDelete
  );
});

// 2. 回调函数记忆化
const CanvasContainer: React.FC = () => {
  const [cards, setCards] = useState<Card[]>([]);

  const handleCardUpdate = useCallback((id: string, updates: Partial<Card>) => {
    setCards(prevCards =>
      prevCards.map(card =>
        card.id === id ? { ...card, ...updates } : card
      )
    );
  }, []);

  const handleCardDelete = useCallback((id: string) => {
    setCards(prevCards => prevCards.filter(card => card.id !== id));
  }, []);

  return (
    <div>
      {cards.map(card => (
        <CardComponent
          key={card.id}
          card={card}
          onUpdate={handleCardUpdate}
          onDelete={handleCardDelete}
        />
      ))}
    </div>
  );
};

// 3. 虚拟化长列表
const VirtualizedCardList: React.FC<{ cards: Card[] }> = ({ cards }) => {
  const [containerRef, setContainerRef] = useState<HTMLDivElement | null>(null);
  const [scrollTop, setScrollTop] = useState(0);

  const itemHeight = 150;
  const containerHeight = 600;

  const visibleRange = useMemo(() => {
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const start = Math.floor(scrollTop / itemHeight);
    const end = Math.min(start + visibleCount + 1, cards.length);

    return { start: Math.max(0, start - 1), end };
  }, [scrollTop, cards.length, containerHeight, itemHeight]);

  const visibleCards = useMemo(() =>
    cards.slice(visibleRange.start, visibleRange.end),
    [cards, visibleRange]
  );

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  return (
    <div
      ref={setContainerRef}
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={handleScroll}
    >
      <div style={{ height: cards.length * itemHeight, position: 'relative' }}>
        {visibleCards.map((card, index) => (
          <div
            key={card.id}
            style={{
              position: 'absolute',
              top: (visibleRange.start + index) * itemHeight,
              height: itemHeight,
              width: '100%'
            }}
          >
            <CardComponent card={card} />
          </div>
        ))}
      </div>
    </div>
  );
};

---

## **Error Handling Strategy (错误处理策略)**

### **Unified Error Handling (统一错误处理)**

#### **Error Classification System (错误分类系统)**
```typescript
// 错误类型枚举
enum ErrorType {
  VALIDATION = 'VALIDATION',
  DATABASE = 'DATABASE',
  NETWORK = 'NETWORK',
  API = 'API',
  AUTHENTICATION = 'AUTHENTICATION',
  PERMISSION = 'PERMISSION',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  RATE_LIMIT = 'RATE_LIMIT',
  UNKNOWN = 'UNKNOWN'
}

// 错误严重级别
enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 统一错误接口
interface AppErrorInfo {
  type: ErrorType;
  severity: ErrorSeverity;
  code: string;
  message: string;
  userMessage: string;
  context?: Record<string, any>;
  timestamp: Date;
  stack?: string;
}

// 错误工厂
class ErrorFactory {
  static createValidationError(message: string, field?: string): AppErrorInfo {
    return {
      type: ErrorType.VALIDATION,
      severity: ErrorSeverity.LOW,
      code: 'VALIDATION_FAILED',
      message,
      userMessage: `输入验证失败: ${message}`,
      context: { field },
      timestamp: new Date()
    };
  }

  static createDatabaseError(message: string, query?: string): AppErrorInfo {
    return {
      type: ErrorType.DATABASE,
      severity: ErrorSeverity.HIGH,
      code: 'DATABASE_ERROR',
      message,
      userMessage: '数据操作失败，请稍后重试',
      context: { query },
      timestamp: new Date()
    };
  }

  static createAPIError(message: string, status?: number, response?: any): AppErrorInfo {
    return {
      type: ErrorType.API,
      severity: status && status >= 500 ? ErrorSeverity.HIGH : ErrorSeverity.MEDIUM,
      code: 'API_ERROR',
      message,
      userMessage: 'AI服务暂时不可用，请稍后重试',
      context: { status, response },
      timestamp: new Date()
    };
  }

  static createNetworkError(message: string): AppErrorInfo {
    return {
      type: ErrorType.NETWORK,
      severity: ErrorSeverity.MEDIUM,
      code: 'NETWORK_ERROR',
      message,
      userMessage: '网络连接失败，请检查网络设置',
      timestamp: new Date()
    };
  }
}
```

#### **React Error Boundaries (React错误边界)**
```typescript
// 全局错误边界
interface ErrorBoundaryState {
  hasError: boolean;
  error?: AppErrorInfo;
  errorId?: string;
}

class GlobalErrorBoundary extends React.Component<
  { children: React.ReactNode },
  ErrorBoundaryState
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    const errorId = generateUUID();
    const appError = ErrorFactory.createUnknownError(error.message, error.stack);

    return {
      hasError: true,
      error: appError,
      errorId
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const errorId = this.state.errorId!;

    // 记录错误
    ErrorLogger.logError({
      ...this.state.error!,
      stack: error.stack,
      context: {
        ...this.state.error!.context,
        componentStack: errorInfo.componentStack,
        errorBoundary: 'GlobalErrorBoundary'
      }
    }, errorId);

    // 发送错误报告
    ErrorReporter.reportError(this.state.error!, errorId);
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorFallback
          error={this.state.error!}
          errorId={this.state.errorId!}
          onRetry={() => this.setState({ hasError: false })}
        />
      );
    }

    return this.props.children;
  }
}

// 特定功能错误边界
const CanvasErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary
      fallback={({ error, retry }) => (
        <div className="canvas-error-fallback">
          <h3>画布加载失败</h3>
          <p>画布功能遇到问题，请尝试刷新页面</p>
          <button onClick={retry}>重试</button>
          <button onClick={() => window.location.reload()}>刷新页面</button>
        </div>
      )}
      onError={(error) => {
        ErrorLogger.logError(ErrorFactory.createCanvasError(error.message));
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

// 错误回退组件
const ErrorFallback: React.FC<{
  error: AppErrorInfo;
  errorId: string;
  onRetry: () => void;
}> = ({ error, errorId, onRetry }) => {
  const [showDetails, setShowDetails] = useState(false);

  return (
    <div className="error-fallback">
      <div className="error-content">
        <h2>出现了一些问题</h2>
        <p>{error.userMessage}</p>

        <div className="error-actions">
          <button onClick={onRetry} className="retry-button">
            重试
          </button>
          <button
            onClick={() => window.location.reload()}
            className="reload-button"
          >
            刷新页面
          </button>
        </div>

        <details className="error-details">
          <summary>技术详情</summary>
          <div className="error-info">
            <p><strong>错误ID:</strong> {errorId}</p>
            <p><strong>错误类型:</strong> {error.type}</p>
            <p><strong>错误代码:</strong> {error.code}</p>
            <p><strong>时间:</strong> {error.timestamp.toLocaleString()}</p>
            {error.context && (
              <pre>{JSON.stringify(error.context, null, 2)}</pre>
            )}
          </div>
        </details>
      </div>
    </div>
  );
};
```

#### **Database Error Handling (数据库错误处理)**
```typescript
class DatabaseErrorHandler {
  static async handleDatabaseOperation<T>(
    operation: () => Promise<T>,
    context: string
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      const dbError = this.classifyDatabaseError(error, context);
      ErrorLogger.logError(dbError);

      // 根据错误类型决定是否重试
      if (this.shouldRetry(dbError)) {
        return this.retryOperation(operation, context);
      }

      throw new DatabaseOperationError(dbError.userMessage, dbError);
    }
  }

  private static classifyDatabaseError(error: any, context: string): AppErrorInfo {
    if (error.message?.includes('UNIQUE constraint failed')) {
      return ErrorFactory.createConflictError('数据已存在', context);
    } else if (error.message?.includes('FOREIGN KEY constraint failed')) {
      return ErrorFactory.createValidationError('关联数据不存在', context);
    } else if (error.message?.includes('database is locked')) {
      return ErrorFactory.createDatabaseError('数据库忙碌，请稍后重试', context);
    } else {
      return ErrorFactory.createDatabaseError(error.message || '数据库操作失败', context);
    }
  }

  private static shouldRetry(error: AppErrorInfo): boolean {
    return error.code === 'DATABASE_LOCKED' || error.code === 'TEMPORARY_ERROR';
  }

  private static async retryOperation<T>(
    operation: () => Promise<T>,
    context: string,
    maxRetries = 3
  ): Promise<T> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await new Promise(resolve => setTimeout(resolve, attempt * 100));
        return await operation();
      } catch (error) {
        if (attempt === maxRetries) {
          throw error;
        }
        console.warn(`Database operation retry ${attempt}/${maxRetries} failed:`, error);
      }
    }
    throw new Error('Max retries exceeded');
  }
}
```

#### **AI Service Error Handling (AI服务错误处理)**
```typescript
class AIServiceErrorHandler {
  static async handleAIRequest<T>(
    request: () => Promise<T>,
    context: { conversationId?: string; model?: string }
  ): Promise<T> {
    try {
      return await request();
    } catch (error) {
      const aiError = this.classifyAIError(error, context);
      ErrorLogger.logError(aiError);

      // 用户友好的错误处理
      this.showUserFriendlyError(aiError);

      throw new AIServiceError(aiError.userMessage, aiError);
    }
  }

  private static classifyAIError(error: any, context: any): AppErrorInfo {
    const status = error.response?.status || error.status;

    switch (status) {
      case 401:
        return ErrorFactory.createAuthenticationError('API密钥无效或已过期');
      case 429:
        return ErrorFactory.createRateLimitError('请求频率过高，请稍后重试');
      case 500:
      case 502:
      case 503:
        return ErrorFactory.createAPIError('AI服务暂时不可用', status);
      default:
        if (error.code === 'NETWORK_ERROR') {
          return ErrorFactory.createNetworkError('网络连接失败');
        }
        return ErrorFactory.createAPIError(error.message || 'AI请求失败', status);
    }
  }

  private static showUserFriendlyError(error: AppErrorInfo): void {
    // 显示用户友好的错误提示
    toast.error(error.userMessage, {
      duration: 5000,
      action: error.type === ErrorType.AUTHENTICATION ? {
        label: '设置API密钥',
        onClick: () => openSettingsModal()
      } : undefined
    });
  }
}
```

---

## **Monitoring and Observability (监控与可观测性)**

### **Performance Monitoring (性能监控)**

#### **Web Vitals Monitoring (Web Vitals监控)**
```typescript
// Web Vitals监控实现
class WebVitalsMonitor {
  private metrics: Map<string, number> = new Map();
  private observers: PerformanceObserver[] = [];

  initialize(): void {
    this.observeLCP();
    this.observeFID();
    this.observeCLS();
    this.observeFCP();
    this.observeTTFB();
  }

  private observeLCP(): void {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1] as PerformanceEntry;

      this.metrics.set('LCP', lastEntry.startTime);
      this.reportMetric('LCP', lastEntry.startTime);
    });

    observer.observe({ entryTypes: ['largest-contentful-paint'] });
    this.observers.push(observer);
  }

  private observeFID(): void {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        this.metrics.set('FID', entry.processingStart - entry.startTime);
        this.reportMetric('FID', entry.processingStart - entry.startTime);
      });
    });

    observer.observe({ entryTypes: ['first-input'] });
    this.observers.push(observer);
  }

  private observeCLS(): void {
    let clsValue = 0;
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });

      this.metrics.set('CLS', clsValue);
      this.reportMetric('CLS', clsValue);
    });

    observer.observe({ entryTypes: ['layout-shift'] });
    this.observers.push(observer);
  }

  private observeFCP(): void {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        this.metrics.set('FCP', entry.startTime);
        this.reportMetric('FCP', entry.startTime);
      });
    });

    observer.observe({ entryTypes: ['paint'] });
    this.observers.push(observer);
  }

  private observeTTFB(): void {
    const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigationEntry) {
      const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
      this.metrics.set('TTFB', ttfb);
      this.reportMetric('TTFB', ttfb);
    }
  }

  private reportMetric(name: string, value: number): void {
    console.log(`[WebVitals] ${name}: ${value.toFixed(2)}ms`);

    // 发送到分析服务
    this.sendToAnalytics(name, value);

    // 检查性能阈值
    this.checkPerformanceThresholds(name, value);
  }

  private sendToAnalytics(metric: string, value: number): void {
    // 在生产环境中发送到分析服务
    if (typeof gtag !== 'undefined') {
      gtag('event', 'web_vitals', {
        metric_name: metric,
        metric_value: Math.round(value),
        custom_map: { metric_name: 'custom_metric_name' }
      });
    }
  }

  private checkPerformanceThresholds(metric: string, value: number): void {
    const thresholds = {
      LCP: { good: 2500, poor: 4000 },
      FID: { good: 100, poor: 300 },
      CLS: { good: 0.1, poor: 0.25 },
      FCP: { good: 1800, poor: 3000 },
      TTFB: { good: 800, poor: 1800 }
    };

    const threshold = thresholds[metric as keyof typeof thresholds];
    if (!threshold) return;

    let status: 'good' | 'needs-improvement' | 'poor';
    if (value <= threshold.good) {
      status = 'good';
    } else if (value <= threshold.poor) {
      status = 'needs-improvement';
    } else {
      status = 'poor';
    }

    if (status === 'poor') {
      console.warn(`[Performance Warning] ${metric} is poor: ${value.toFixed(2)}ms`);
    }
  }

  getMetrics(): Record<string, number> {
    return Object.fromEntries(this.metrics);
  }

  destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics.clear();
  }
}
```

#### **Custom Performance Monitor (自定义性能监控)**
```typescript
// 自定义性能监控器
class CustomPerformanceMonitor {
  private timers: Map<string, number> = new Map();
  private metrics: PerformanceMetric[] = [];

  // 开始计时
  startTimer(name: string): void {
    this.timers.set(name, performance.now());
  }

  // 结束计时并记录
  endTimer(name: string, metadata?: Record<string, any>): number {
    const startTime = this.timers.get(name);
    if (!startTime) {
      console.warn(`Timer "${name}" was not started`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(name);

    const metric: PerformanceMetric = {
      name,
      duration,
      timestamp: new Date(),
      metadata
    };

    this.metrics.push(metric);
    this.reportMetric(metric);

    return duration;
  }

  // 测量函数执行时间
  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    this.startTimer(name);
    try {
      const result = await fn();
      this.endTimer(name, { success: true });
      return result;
    } catch (error) {
      this.endTimer(name, { success: false, error: error.message });
      throw error;
    }
  }

  // 测量同步函数执行时间
  measure<T>(name: string, fn: () => T): T {
    this.startTimer(name);
    try {
      const result = fn();
      this.endTimer(name, { success: true });
      return result;
    } catch (error) {
      this.endTimer(name, { success: false, error: error.message });
      throw error;
    }
  }

  // 记录自定义指标
  recordMetric(name: string, value: number, unit: string, metadata?: Record<string, any>): void {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: new Date(),
      metadata
    };

    this.metrics.push(metric);
    this.reportMetric(metric);
  }

  private reportMetric(metric: PerformanceMetric): void {
    console.log(`[Performance] ${metric.name}: ${metric.duration || metric.value}${metric.unit || 'ms'}`, metric.metadata);
  }

  // 获取性能报告
  getPerformanceReport(): PerformanceReport {
    const now = new Date();
    const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const recentMetrics = this.metrics.filter(m => m.timestamp > last24h);

    return {
      totalMetrics: recentMetrics.length,
      averageResponseTime: this.calculateAverage(recentMetrics, 'duration'),
      slowestOperations: this.getSlowOperations(recentMetrics),
      errorRate: this.calculateErrorRate(recentMetrics),
      memoryUsage: this.getMemoryUsage(),
      timestamp: now
    };
  }

  private calculateAverage(metrics: PerformanceMetric[], field: keyof PerformanceMetric): number {
    const values = metrics.map(m => m[field] as number).filter(v => typeof v === 'number');
    return values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0;
  }

  private getSlowOperations(metrics: PerformanceMetric[]): PerformanceMetric[] {
    return metrics
      .filter(m => m.duration && m.duration > 1000)
      .sort((a, b) => (b.duration || 0) - (a.duration || 0))
      .slice(0, 10);
  }

  private calculateErrorRate(metrics: PerformanceMetric[]): number {
    const total = metrics.length;
    const errors = metrics.filter(m => m.metadata?.success === false).length;
    return total > 0 ? (errors / total) * 100 : 0;
  }

  private getMemoryUsage(): MemoryUsage | null {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit
      };
    }
    return null;
  }
}

interface PerformanceMetric {
  name: string;
  duration?: number;
  value?: number;
  unit?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

interface PerformanceReport {
  totalMetrics: number;
  averageResponseTime: number;
  slowestOperations: PerformanceMetric[];
  errorRate: number;
  memoryUsage: MemoryUsage | null;
  timestamp: Date;
}

interface MemoryUsage {
  used: number;
  total: number;
  limit: number;
}

### **User Analytics (用户分析)**

#### **User Behavior Tracking (用户行为跟踪)**
```typescript
// 用户行为分析器
class UserAnalytics {
  private events: UserEvent[] = [];
  private sessionId: string;
  private userId?: string;

  constructor() {
    this.sessionId = generateUUID();
    this.initializeSession();
  }

  private initializeSession(): void {
    this.trackEvent('session_start', {
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      referrer: document.referrer
    });
  }

  // 跟踪用户事件
  trackEvent(eventName: string, properties?: Record<string, any>): void {
    const event: UserEvent = {
      id: generateUUID(),
      sessionId: this.sessionId,
      userId: this.userId,
      name: eventName,
      properties: properties || {},
      timestamp: new Date(),
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    this.events.push(event);
    this.processEvent(event);
  }

  // 跟踪卡片操作
  trackCardAction(action: string, cardId: string, properties?: Record<string, any>): void {
    this.trackEvent('card_action', {
      action,
      cardId,
      ...properties
    });
  }

  // 跟踪AI交互
  trackAIInteraction(action: string, properties?: Record<string, any>): void {
    this.trackEvent('ai_interaction', {
      action,
      ...properties
    });
  }

  private processEvent(event: UserEvent): void {
    // 本地存储（用于离线分析）
    this.storeEventLocally(event);

    // 实时分析
    this.analyzeEvent(event);
  }

  private storeEventLocally(event: UserEvent): void {
    try {
      const stored = localStorage.getItem('user_events') || '[]';
      const events = JSON.parse(stored);
      events.push(event);

      // 保留最近1000个事件
      if (events.length > 1000) {
        events.splice(0, events.length - 1000);
      }

      localStorage.setItem('user_events', JSON.stringify(events));
    } catch (error) {
      console.warn('Failed to store user event locally:', error);
    }
  }

  private analyzeEvent(event: UserEvent): void {
    // 实时用户行为分析
    switch (event.name) {
      case 'card_action':
        this.analyzeCardUsage(event);
        break;
      case 'ai_interaction':
        this.analyzeAIUsage(event);
        break;
    }
  }

  private analyzeCardUsage(event: UserEvent): void {
    const recentCardActions = this.getRecentEvents('card_action', 300000); // 5分钟内

    // 检测高频操作
    if (recentCardActions.length > 50) {
      console.log('High frequency card operations detected');
    }
  }

  private analyzeAIUsage(event: UserEvent): void {
    const recentAIEvents = this.getRecentEvents('ai_interaction', 3600000); // 1小时内

    // 分析AI使用频率
    console.log(`AI interactions in last hour: ${recentAIEvents.length}`);
  }

  private getRecentEvents(eventName: string, timeWindowMs: number): UserEvent[] {
    const cutoff = new Date(Date.now() - timeWindowMs);
    return this.events.filter(e =>
      e.name === eventName && e.timestamp > cutoff
    );
  }

  // 获取用户行为报告
  getUserBehaviorReport(): UserBehaviorReport {
    const now = new Date();
    const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const recentEvents = this.events.filter(e => e.timestamp > last24h);

    return {
      sessionId: this.sessionId,
      totalEvents: recentEvents.length,
      cardActions: recentEvents.filter(e => e.name === 'card_action').length,
      aiInteractions: recentEvents.filter(e => e.name === 'ai_interaction').length,
      sessionDuration: now.getTime() - this.events[0]?.timestamp.getTime() || 0,
      timestamp: now
    };
  }
}

interface UserEvent {
  id: string;
  sessionId: string;
  userId?: string;
  name: string;
  properties: Record<string, any>;
  timestamp: Date;
  url: string;
  userAgent: string;
}

interface UserBehaviorReport {
  sessionId: string;
  totalEvents: number;
  cardActions: number;
  aiInteractions: number;
  sessionDuration: number;
  timestamp: Date;
}
```

---

## **Checklist Results Report (检查清单结果报告)**

### **Architecture Validation Summary (架构验证总结)**

基于架构检查清单的全面评估，本架构文档的验证结果如下：

#### **Overall Assessment (总体评估)**

**总体通过率: 97.2%**

**实施准备度: HIGH (高)**

**关键优势:**
1. **架构完整性**: 覆盖了从前端到数据层的完整技术栈
2. **创新设计**: Browser-as-Backend架构适合本地优先应用
3. **代码质量**: 详细的编码标准和测试策略
4. **用户体验**: 考虑了性能优化和错误处理
5. **可维护性**: 清晰的组件架构和服务层设计

**主要验证结果:**
- ✅ High Level Architecture (100%)
- ✅ Tech Stack (95%)
- ✅ Data Models (100%)
- ✅ API Specification (90%)
- ✅ Components (100%)
- ✅ External APIs (95%)
- ✅ Core Workflows (100%)
- ✅ Database Schema (100%)
- ✅ Frontend Architecture (100%)
- ✅ Backend Architecture (95%)
- ✅ Project Structure (100%)
- ✅ Development Workflow (100%)
- ✅ Deployment Architecture (90%)
- ✅ Security and Performance (95%)
- ✅ Testing Strategy (100%)
- ✅ Coding Standards (100%)
- ✅ Error Handling Strategy (100%)
- ✅ Monitoring and Observability (100%)

**改进建议:**
1. **依赖管理**: 补充更详细的依赖冲突解决策略
2. **API监控**: 增强OpenRouter API调用监控
3. **备份策略**: 补充数据备份和恢复机制
4. **分发策略**: 详细的应用分发和更新策略
5. **安全审计**: 定期安全审计和漏洞扫描流程

**结论**: 本架构文档为卡片画布应用提供了坚实的技术基础，具备高度的实施可行性。建议的改进点主要集中在运维和安全方面，不影响核心功能的开发实施。

---

**文档完成时间**: 2024年1月31日
**文档版本**: 1.0.0
**架构师**: Winston (BMad Architect)
**总页数**: 约150页
**总行数**: 5,050+行

**下一步建议**:
1. 开始项目初始化和环境搭建
2. 实施核心数据模型和数据库架构
3. 开发基础组件和服务层
4. 逐步实现Epic 1的核心功能