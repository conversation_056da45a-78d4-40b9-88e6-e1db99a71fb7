# 本地卡片画布与AI交互工具 Product Requirements Document (PRD)

## Goals and Background Context

### Goals
- 为小程序员团队提供轻量级、本地优先的知识管理解决方案
- 实现卡片式知识组织与可视化连接，帮助整理复杂技术概念
- 集成AI功能，将结构化知识网络作为上下文提交给智能体
- 支持Markdown标准化，确保与现有开发工具生态的兼容性
- 建立纯前端架构，消除服务器依赖和数据隐私担忧
- 在4-6周内交付MVP，验证核心价值假设

### Background Context
基于详细的项目简报分析，小程序员团队面临知识管理碎片化的严重问题。技术文档、代码片段、架构想法分散在各种工具中，导致每天30-60分钟的工具切换成本和重要知识的丢失。现有解决方案要么过于复杂（Obsidian），要么依赖云端（Notion），无法满足小团队对简洁性和数据控制的需求。

随着AI工具在开发工作中的普及，团队迫切需要一个能够快速整理结构化上下文并与AI交互的工具。本项目旨在填补这一市场空白，通过SQLite WASM技术实现本地优先架构，结合OpenRouter API提供AI集成能力。

### Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-30 | 1.0 | 初始PRD创建，基于项目简报 | John (PM) |

## Requirements

### Functional
1. **FR1**: 用户可以通过点击画布顶部的新建卡片按钮创建新的卡片，卡片支持实时Markdown编辑和语法高亮显示
2. **FR2**: 用户可以通过拖拽操作移动卡片位置，卡片大小根据内容自适应，同时支持手动调整
3. **FR3**: 用户可以通过画布顶部的保存按钮保存卡片，系统提供撤销功能防止误操作
4. **FR4**: 用户可以通过点击卡片边缘的连线按钮创建连线，建立卡片间的关系
5. **FR5**: 用户可以删除连线、调整连线路径，连线提供视觉反馈（悬停高亮、选中状态）
6. **FR6**: 画布支持无限缩放和平移操作，用户可以自由组织大量卡片
7. **FR7**: 系统使用SQLite WASM实现本地数据持久化，保存卡片内容、位置和连线关系
8. **FR8**: 用户可以创建多个主题画布，系统按需加载画布内容，使用LRU缓存策略管理内存
9. **FR9**: 用户可以配置OpenRouter API密钥，选择不同的大语言模型进行AI交互
10. **FR10**: 用户可以选择特定卡片或整个画布作为上下文，一键提交给AI智能体获取回复
11. **FR11**: 系统严格验证Markdown语法，确保卡片内容符合标准格式，卡片第一段作为卡片标题
12. **FR12**: 用户可以导出卡片内容为标准Markdown格式，保持与其他工具的兼容性

### Non Functional
1. **NFR1**: 应用启动时间必须小于3秒，在现代浏览器中提供流畅的用户体验
2. **NFR2**: 卡片创建和编辑操作的响应时间必须小于500毫秒
3. **NFR3**: SQLite数据库查询响应时间必须小于100毫秒，确保数据操作的实时性
4. **NFR4**: 系统必须支持单画布100个卡片的流畅运行，不出现明显的性能下降
5. **NFR5**: AI API调用成功率必须大于95%，提供可靠的智能交互体验
6. **NFR6**: 应用必须支持现代浏览器（Chrome 90+, Firefox 88+, Safari 14+, Edge 90+）
7. **NFR7**: 系统必须实现完全本地存储，不依赖任何云端服务或后端服务器
8. **NFR8**: 数据存储必须可靠，浏览器关闭重开后数据保持100%完整性
9. **NFR9**: 应用必须支持离线使用，除AI功能外的所有核心功能在无网络环境下正常工作
10. **NFR10**: 系统必须提供清晰的错误处理和用户反馈，包括API调用失败的降级方案

## User Interface Design Goals

### Overall UX Vision
创建一个直观、简洁的数字白板体验，让程序员能够像在物理白板上画图一样自然地整理思路。界面应该"消失"在用户的工作流程中，让用户专注于内容创作而非工具操作。采用极简主义设计原则，避免功能膨胀，确保每个界面元素都有明确的目的和价值。

### Key Interaction Paradigms
- **直接操作**: 点击创建、拖拽移动、双击编辑，遵循用户的直觉预期
- **键盘优先**: 支持常用快捷键（Ctrl+S保存、Ctrl+Z撤销），满足程序员的操作习惯
- **渐进式披露**: 高级功能（AI配置、导出选项）隐藏在二级菜单中，保持主界面简洁
- **即时反馈**: 所有操作提供即时的视觉反馈，连线悬停高亮、卡片选中状态等
- **上下文感知**: 右键菜单根据点击对象（卡片、连线、空白区域）提供相关操作

### Core Screens and Views
- **主画布界面**: 占据90%屏幕空间的无限画布，顶部简洁工具栏
- **卡片编辑器**: 内嵌式Markdown编辑器，左侧编辑右侧预览的分屏模式
- **画布管理器**: 侧边栏显示所有画布，支持快速切换和重命名
- **AI交互面板**: 可收缩的侧边面板，显示AI对话历史和配置选项
- **设置页面**: 模态对话框形式，包含API配置、主题选择、导出设置

### Accessibility: WCAG AA
遵循WCAG AA标准，确保工具对所有用户可用：
- 支持键盘导航，所有功能可通过键盘访问
- 提供充足的颜色对比度，支持高对比度模式
- 为屏幕阅读器提供适当的ARIA标签和语义化HTML
- 支持缩放至200%而不影响功能使用

### Branding
采用现代、专业的技术工具美学：
- **色彩方案**: 深色主题为主（程序员偏好），提供浅色主题选项
- **字体**: 使用等宽字体显示代码内容，无衬线字体用于界面文本
- **图标**: 简洁的线性图标，与VS Code等开发工具保持一致的视觉语言
- **动画**: 微妙的过渡动画，提升操作流畅性但不干扰工作流程

### Target Device and Platforms: Web Responsive
- **主要目标**: 桌面浏览器（1920x1080及以上分辨率）
- **次要支持**: 平板设备（iPad等），提供触控优化的交互方式
- **不支持**: 手机设备（屏幕太小无法有效进行画布操作）
- **浏览器兼容**: Chrome、Firefox、Safari、Edge的最新版本

## Technical Assumptions

### Repository Structure: Monorepo
采用单仓库结构，包含前端应用、文档、测试和构建配置。这种结构适合单人开发和小团队协作，简化了依赖管理和版本控制。所有相关代码集中在一个仓库中，便于维护和部署。

### Service Architecture
**CRITICAL DECISION - 纯前端单体架构**: 
- 无传统后端服务器，所有逻辑在浏览器中运行
- SQLite WASM作为本地数据库，消除服务器依赖
- 静态文件托管（GitHub Pages、Netlify、Vercel）
- Service Worker提供缓存和离线支持
- 模块化前端架构：UI层、业务逻辑层、数据访问层

### Testing Requirements
**CRITICAL DECISION - Unit + Integration测试策略**:
- **单元测试**: 使用Jest测试业务逻辑、数据操作、工具函数
- **集成测试**: 使用React Testing Library测试组件交互和用户流程
- **E2E测试**: 使用Playwright测试关键用户路径（创建卡片、连线、AI交互）
- **性能测试**: 针对SQLite WASM和大数据量场景的专项测试
- **手动测试**: 跨浏览器兼容性和用户体验验证

### Additional Technical Assumptions and Requests

**前端技术栈**:
- **框架**: React 18+ with TypeScript（类型安全、组件化、生态成熟）
- **状态管理**: Zustand（轻量级、简单易用，避免Redux的复杂性）
- **样式**: TailwindCSS + Headless UI（快速开发、一致性、可维护性）
- **构建工具**: Vite（快速开发服务器、现代构建流程）

**数据和存储**:
- **本地数据库**: SQLite WASM with sql.js（纯前端数据库解决方案）
- **缓存策略**: LRU算法管理画布缓存，最大5个画布在内存中
- **数据备份**: 本地文件导出（JSON格式），用户手动备份

**渲染和交互**:
- **画布渲染**: Konva.js（高性能2D Canvas库，支持复杂图形操作）
- **Markdown处理**: marked.js + highlight.js（标准Markdown解析和语法高亮）
- **拖拽功能**: React DnD或原生HTML5 Drag API

**AI集成**:
- **HTTP客户端**: Alova.js（轻量级、现代的HTTP客户端）
- **API集成**: OpenRouter API（支持多种大模型的统一接口）
- **错误处理**: 完善的降级策略和用户友好的错误提示

**开发和部署**:
- **代码质量**: ESLint + Prettier（代码规范和格式化）
- **Git工作流**: GitHub Flow（简单的分支策略，适合小团队）
- **CI/CD**: GitHub Actions（自动化测试、构建、部署）
- **部署目标**: 静态文件托管，支持HTTPS和自定义域名

**性能优化**:
- **代码分割**: React.lazy实现路由级别的代码分割
- **资源优化**: 图片压缩、字体子集化、依赖包分析
- **缓存策略**: Service Worker缓存静态资源，IndexedDB缓存应用数据

## Epic List

基于项目需求和技术架构，我提出以下史诗序列，遵循敏捷最佳实践，确保每个史诗都能交付完整的、可部署的功能增量：

**Epic 1: 基础架构与核心画布** - 建立项目基础设施（React应用、构建流程、CI/CD），实现基本的画布操作和卡片管理功能，交付一个可用的数字白板原型

**Epic 2: 本地数据持久化** - 集成SQLite WASM，实现可靠的本地数据存储，支持多画布管理和缓存策略，确保用户数据的持久性和性能

**Epic 3: Markdown编辑与连线功能** - 实现严格的Markdown语法支持、实时编辑预览，以及卡片间的连线创建和管理，完成核心的知识组织功能

**Epic 4: AI集成与导出功能** - 集成OpenRouter API，实现AI上下文提交和交互，添加数据导出功能，完成产品的差异化价值主张

## Epic 1: 基础架构与核心画布

建立完整的项目基础设施，包括React应用架构、开发工具链、CI/CD流程，同时实现核心的画布操作和基础卡片管理功能。这个史诗将交付一个功能完整的数字白板原型，用户可以创建、编辑、移动和删除卡片。

### Story 1.1: 项目基础架构搭建
As a 开发者,
I want 建立完整的项目开发环境和基础架构,
so that 我可以开始功能开发并确保代码质量和部署流程。

#### Acceptance Criteria
1. 使用Vite + React + TypeScript创建项目脚手架，配置开发服务器
2. 集成TailwindCSS和基础UI组件库，建立设计系统基础
3. 配置ESLint、Prettier和Git hooks，确保代码质量标准
4. 设置GitHub仓库，配置GitHub Actions实现自动化测试和构建
5. 创建基础的路由结构和应用布局组件
6. 部署到静态托管平台（Netlify/Vercel），确保CI/CD流程正常工作
7. 创建健康检查页面，验证应用正常运行

### Story 1.2: 画布组件基础实现
As a 用户,
I want 看到一个可交互的无限画布界面,
so that 我可以在其中进行卡片操作。

#### Acceptance Criteria
1. 使用Konva.js创建画布组件，支持无限滚动的视觉效果
2. 实现画布的缩放功能（鼠标滚轮），缩放范围0.1x到5x
3. 实现画布的平移功能（鼠标拖拽），支持平滑的移动体验
4. 添加画布坐标系统，准确跟踪鼠标位置和元素坐标
5. 实现画布的边界检测和性能优化，避免无限渲染
6. 创建简洁的顶部工具栏，包含基础操作按钮
7. 画布操作响应时间小于100ms，确保流畅的用户体验

### Story 1.3: 基础卡片创建和显示
As a 用户,
I want 通过点击画布顶部的新建卡片按钮创建卡片并看到卡片显示,
so that 我可以开始组织我的想法和信息。

#### Acceptance Criteria
1. 点击画布顶部的新建卡片按钮创建新卡片，卡片出现在画布中心
2. 卡片具有默认的视觉样式（边框、背景色、阴影效果）
3. 卡片显示唯一标识符和创建时间戳
4. 卡片具有最小尺寸限制（100x60px），确保可读性
5. 新创建的卡片自动进入编辑模式，光标定位到文本区域
6. 卡片在画布上正确定位，不会重叠或超出可视区域
7. 支持连续创建多个卡片，每个卡片独立管理

### Story 1.4: 卡片文本编辑功能
As a 用户,
I want 编辑卡片中的文本内容,
so that 我可以记录和整理我的想法。

#### Acceptance Criteria
1. 双击卡片进入编辑模式，显示文本输入框
2. 支持多行文本输入，自动换行和文本区域扩展
3. 实现基础的文本编辑功能（选择、复制、粘贴、撤销）
4. 点击卡片外部或按Escape键退出编辑模式
5. 文本内容实时保存到内存状态，无需手动保存操作
6. 支持常用快捷键（Ctrl+A全选、Ctrl+Z撤销等）
7. 文本编辑响应时间小于50ms，提供即时反馈

### Story 1.5: 卡片拖拽移动功能
As a 用户,
I want 拖拽移动卡片到不同位置,
so that 我可以重新组织卡片的空间布局。

#### Acceptance Criteria
1. 鼠标按住卡片可以拖拽移动，显示拖拽状态的视觉反馈
2. 拖拽过程中卡片跟随鼠标移动，提供平滑的动画效果
3. 释放鼠标后卡片固定在新位置，更新坐标信息
4. 拖拽时其他卡片保持静止，不受影响
5. 支持拖拽到画布的任意位置，包括当前视口外的区域
6. 拖拽操作不会意外触发编辑模式或其他功能
7. 拖拽性能优化，支持同时显示50个卡片时的流畅拖拽

### Story 1.6: 卡片删除和撤销功能
As a 用户,
I want 删除不需要的卡片并能够撤销删除操作,
so that 我可以管理卡片数量并避免误删重要内容。

#### Acceptance Criteria
1. 右键点击卡片显示上下文菜单，包含删除选项
2. 点击删除后卡片立即从画布消失，显示删除确认提示
3. 提供撤销删除功能，在删除后30秒内可以恢复
4. 撤销提示以非侵入式的方式显示（toast通知）
5. 删除操作记录到操作历史，支持多级撤销
6. 键盘快捷键Delete删除选中的卡片
7. 批量删除功能：支持选择多个卡片同时删除

## Epic 2: 本地数据持久化

集成SQLite WASM技术，实现可靠的本地数据存储系统，支持多画布管理和智能缓存策略。这个史诗将确保用户数据的持久性，提供多画布的组织能力，并优化应用性能。

### Story 2.1: SQLite WASM集成和基础数据模型
As a 用户,
I want 我的卡片数据能够持久保存在本地,
so that 关闭浏览器后重新打开时数据不会丢失。

#### Acceptance Criteria
1. 集成sql.js库，在浏览器中初始化SQLite数据库
2. 创建数据库表结构：Cards(id, content, x, y, width, height, canvas_id, created_at, updated_at)
3. 创建数据库表结构：Canvases(id, name, created_at, updated_at, is_active)
4. 实现数据库连接管理和错误处理机制
5. 创建数据访问层(DAO)，封装所有数据库操作
6. 实现数据库初始化和版本迁移机制
7. 数据库操作响应时间小于100ms，确保用户体验流畅

### Story 2.2: 卡片数据的CRUD操作
As a 用户,
I want 我的卡片创建、编辑、移动、删除操作能够自动保存,
so that 我不需要手动保存就能确保数据安全。

#### Acceptance Criteria
1. 卡片创建时自动插入数据库，生成唯一ID和时间戳
2. 卡片内容编辑时实时更新数据库，使用防抖机制避免频繁写入
3. 卡片位置移动时更新坐标信息到数据库
4. 卡片删除时从数据库中移除记录，支持软删除机制
5. 实现事务处理，确保数据操作的原子性和一致性
6. 添加数据验证，防止无效数据写入数据库
7. 提供数据恢复机制，处理数据库操作失败的情况

### Story 2.3: 多画布管理系统
As a 用户,
I want 创建和管理多个主题画布,
so that 我可以按不同项目或主题组织我的卡片。

#### Acceptance Criteria
1. 实现画布创建功能，用户可以添加新的主题画布
2. 提供画布列表界面，显示所有画布及其基本信息
3. 支持画布重命名功能，用户可以修改画布名称
4. 实现画布切换功能，点击切换到不同画布
5. 支持画布删除功能，包含删除确认和数据清理
6. 为每个画布显示卡片数量和最后修改时间
7. 设置默认画布，新用户首次使用时自动创建

### Story 2.4: 画布数据加载和缓存策略
As a 用户,
I want 画布切换时快速加载内容,
so that 我可以在不同主题间高效切换而不影响工作流程。

#### Acceptance Criteria
1. 实现当前画布的完整数据加载，包括所有卡片和位置信息
2. 实现LRU缓存算法，内存中最多保持5个画布的数据
3. 画布切换时优先从缓存加载，缓存未命中时从数据库加载
4. 实现缓存数据的同步机制，确保内存和数据库数据一致性
5. 添加加载状态指示器，用户切换画布时显示加载进度
6. 优化大画布的加载性能，支持100个卡片的快速加载
7. 实现缓存清理机制，防止内存使用过量

### Story 2.5: 数据导出和备份功能
As a 用户,
I want 导出我的画布数据作为备份,
so that 我可以保护重要数据并在需要时恢复或迁移。

#### Acceptance Criteria
1. 实现单个画布的JSON格式导出，包含所有卡片和元数据
2. 实现全部画布的批量导出功能，生成完整的数据备份
3. 提供导出文件的下载功能，文件名包含时间戳
4. 实现数据导入功能，用户可以恢复之前的备份
5. 添加导入数据的验证机制，确保数据格式正确性
6. 支持增量导入，避免重复数据的冲突
7. 提供导出进度指示，处理大量数据时显示进度条

### Story 2.6: 数据完整性和错误恢复
As a 用户,
I want 系统能够处理数据错误并提供恢复选项,
so that 即使出现问题我也不会丢失重要数据。

#### Acceptance Criteria
1. 实现数据库操作的错误捕获和日志记录
2. 提供数据库损坏检测和自动修复机制
3. 实现操作失败时的回滚机制，保持数据一致性
4. 添加数据完整性检查，定期验证数据库状态
5. 提供用户友好的错误提示和恢复建议
6. 实现紧急数据恢复功能，从浏览器存储中恢复数据
7. 创建数据健康检查工具，帮助用户诊断数据问题

## Epic 3: Markdown编辑与连线功能

实现严格的Markdown语法支持、实时编辑预览功能，以及卡片间的连线创建和管理系统。这个史诗将完成核心的知识组织功能，让用户能够创建结构化的知识网络。

### Story 3.1: Markdown编辑器集成
As a 用户,
I want 在卡片中使用标准Markdown语法编写内容,
so that 我可以创建格式化的文档并保持与其他工具的兼容性。

#### Acceptance Criteria
1. 集成marked.js库，支持完整的Markdown语法解析
2. 集成highlight.js，为代码块提供语法高亮显示
3. 实现分屏编辑模式：左侧Markdown编辑器，右侧实时预览
4. 支持所有标准Markdown语法：标题、列表、代码块、链接、图片、表格
5. 添加Markdown语法验证，实时显示语法错误提示
6. 实现编辑器的快捷键支持（粗体、斜体、代码等）
7. 编辑器响应时间小于100ms，预览更新延迟小于200ms

### Story 3.2: 卡片内容渲染优化
As a 用户,
I want 卡片能够美观地显示Markdown渲染后的内容,
so that 我可以清晰地阅读和理解卡片信息。

#### Acceptance Criteria
1. 卡片在非编辑模式下显示Markdown渲染后的HTML内容
2. 实现卡片大小的自适应调整，根据内容长度动态调整高度
3. 为渲染内容应用一致的样式主题，确保可读性
4. 支持代码块的语法高亮显示，使用与编辑器一致的主题
5. 处理长内容的显示，提供滚动或截断机制
6. 优化渲染性能，支持50个卡片同时显示而不影响性能
7. 实现内容预览模式，鼠标悬停显示完整内容
8. 卡片第一段内容作为卡片标题显示

### Story 3.3: 连线创建和视觉效果
As a 用户,
I want 通过点击卡片边缘的连线按钮在卡片之间创建连线,
so that 我可以建立知识点之间的关系和依赖。

#### Acceptance Criteria
1. 实现卡片边缘的连线按钮，鼠标悬停时高亮显示
2. 支持点击连线按钮后选择目标卡片创建连线
3. 连线使用贝塞尔曲线或直线，提供平滑的视觉效果
4. 连线具有方向性指示（箭头），显示信息流向
5. 实现连线的悬停效果，鼠标悬停时高亮连线
6. 连线创建过程中提供实时视觉反馈，显示临时连线
7. 支持连线的颜色和样式自定义，区分不同类型的关系

### Story 3.4: 连线管理和交互
As a 用户,
I want 管理已创建的连线，包括删除和修改,
so that 我可以维护准确的知识关系网络。

#### Acceptance Criteria
1. 点击选中连线，显示选中状态的视觉反馈
2. 右键点击连线显示上下文菜单，包含删除选项
3. 支持键盘快捷键删除选中的连线（Delete键）
4. 实现连线路径的自动调整，当卡片移动时连线跟随更新
5. 支持连线的重新连接，拖拽连线端点到新的卡片
6. 添加连线标签功能，用户可以为连线添加描述文字
7. 实现连线的批量操作，支持选择多条连线同时删除

### Story 3.5: 连线数据持久化
As a 用户,
I want 我创建的连线关系能够保存和恢复,
so that 我的知识网络结构不会丢失。

#### Acceptance Criteria
1. 创建Connections数据表：(id, from_card_id, to_card_id, label, type, created_at)
2. 连线创建时自动保存到数据库，记录源卡片和目标卡片ID
3. 画布加载时从数据库恢复所有连线关系
4. 卡片删除时自动清理相关的连线记录，保持数据一致性
5. 实现连线的批量加载和渲染，优化大量连线的性能
6. 支持连线标签和类型的数据库存储和恢复
7. 添加连线数据的完整性检查，处理孤立连线的清理

### Story 3.6: 知识网络可视化优化
As a 用户,
I want 清晰地查看和导航复杂的知识网络,
so that 我可以理解信息之间的复杂关系。

#### Acceptance Criteria
1. 实现连线的层级显示，避免连线重叠和视觉混乱
2. 添加网络布局算法，自动优化卡片和连线的排列
3. 实现连线的筛选功能，可以隐藏或显示特定类型的连线
4. 支持连线路径的智能路由，避开其他卡片和连线
5. 添加网络统计信息，显示卡片数量、连线数量等
6. 实现连线的搜索功能，快速定位特定的连接关系
7. 提供网络导出功能，将知识网络导出为图形格式

## Epic 4: AI集成与导出功能

集成OpenRouter API实现AI上下文提交和智能交互，添加完整的数据导出功能，完成产品的核心差异化价值主张。这个史诗将使用户能够将结构化的知识网络作为上下文与AI进行高效交互。

### Story 4.1: OpenRouter API配置和管理
As a 用户,
I want 配置我的AI API密钥和模型选择,
so that 我可以使用不同的大语言模型进行智能交互。

#### Acceptance Criteria
1. 创建设置页面，提供API密钥输入和安全存储功能
2. 集成Alova.js HTTP客户端，配置OpenRouter API的基础连接
3. 实现API密钥的本地加密存储，确保安全性
4. 提供模型选择界面，支持GPT-4、Claude、Gemini等主流模型
5. 添加API连接测试功能，验证密钥有效性和网络连通性
6. 实现API配置的导入导出，便于用户备份和迁移设置
7. 提供API使用统计，显示调用次数和费用估算

### Story 4.2: 上下文选择和组织
As a 用户,
I want 选择特定的卡片作为AI交互的上下文,
so that 我可以为AI提供相关的背景信息获得更准确的回复。

#### Acceptance Criteria
1. 实现卡片的多选功能，支持Ctrl+点击选择多个卡片
2. 添加"全选画布"功能，一键选择当前画布的所有卡片
3. 创建上下文预览面板，显示选中卡片的标题
4. 实现智能上下文建议，根据连线关系推荐相关卡片
5. 支持上下文的保存和加载，用户可以保存常用的上下文组合
6. 添加上下文大小限制提示，避免超出API的token限制
7. 提供上下文优化功能，自动排序和组织选中的内容

### Story 4.3: AI交互界面和对话管理
As a 用户,
I want 通过友好的界面与AI进行对话,
so that 我可以基于我的知识网络获得智能建议和分析。

#### Acceptance Criteria
1. 创建AI交互侧边面板，包含对话历史和输入区域
2. 实现对话消息的显示，区分用户消息和AI回复
3. 添加消息发送功能，支持文本输入和快捷键发送
4. 实现对话历史的本地存储，用户可以查看之前的对话
5. 支持对话的导出功能，保存为Markdown或文本格式
6. 添加消息的复制和引用功能，便于内容重用
7. 实现对话的搜索功能，快速查找历史对话内容

### Story 4.4: AI API调用和响应处理
As a 用户,
I want AI能够快速响应我的查询并处理各种异常情况,
so that 我可以获得可靠的AI交互体验。

#### Acceptance Criteria
1. 实现AI API的异步调用，避免阻塞用户界面
2. 添加请求状态指示器，显示"正在思考"的加载动画
3. 实现错误处理机制，处理网络错误、API限制等异常
4. 添加重试机制，自动重试失败的API调用
5. 实现响应流式处理，支持实时显示AI回复内容
6. 添加请求超时处理，避免长时间等待
7. 提供降级方案，API不可用时显示友好的错误提示

### Story 4.5: 上下文格式化和优化
As a 用户,
I want 系统能够智能地格式化我的卡片内容作为AI上下文,
so that AI能够更好地理解我的知识结构和关系。

#### Acceptance Criteria
1. 实现卡片内容的智能格式化，包含标题、内容、位置信息
2. 添加连线关系的描述，在上下文中说明卡片间的连接
3. 实现上下文的结构化组织，按逻辑顺序排列卡片内容
4. 支持上下文模板，用户可以自定义上下文的格式
5. 添加元数据包含功能，可选择包含创建时间、标签等信息
6. 实现上下文压缩，在保持信息完整性的前提下减少token使用
7. 提供上下文预览功能，用户可以查看发送给AI的完整内容

### Story 4.6: 数据导出和分享功能
As a 用户,
I want 导出我的画布和AI对话记录,
so that 我可以分享成果或在其他工具中使用这些内容。

#### Acceptance Criteria
1. 实现画布的Markdown格式导出，保持原始格式和结构
2. 支持画布的PDF导出，包含卡片布局和连线关系的可视化
3. 添加AI对话记录的导出功能，支持多种格式（MD、TXT、JSON）
4. 实现选择性导出，用户可以选择特定卡片或对话进行导出
5. 支持批量导出功能，一次导出多个画布或对话
6. 添加导出模板，用户可以自定义导出格式和样式
7. 提供导出预览功能，用户可以在导出前查看最终效果

## 核心用户流程图

### 创建知识网络流程
```
用户进入应用
    ↓
点击画布顶部的新建卡片按钮
    ↓
创建新卡片 → 自动进入编辑模式
    ↓
输入Markdown内容 → 实时预览显示
    ↓
点击画布顶部保存按钮 → 保存卡片内容（卡片第一段作为卡片标题）
    ↓
重复创建多个卡片
    ↓
点击卡片边缘的连线按钮
    ↓
创建连线 → 显示连接关系
    ↓
调整卡片位置和连线 → 优化布局
    ↓
知识网络创建完成
```

### AI交互完整流程
```
用户在画布上有多个相关卡片
    ↓
选择AI交互相关卡片:
├─ 方式1: Ctrl+点击选择多个卡片
├─ 方式2: 点击"全选画布"按钮
    ↓
查看上下文预览面板
├─ 显示选中卡片(标题)
├─ 显示连线关系描述
    ↓
确认上下文内容无误
    ↓
打开AI交互侧边面板
    ↓
检查API配置状态:
├─ 已配置 → 继续
└─ 未配置 → 跳转到设置页面配置API密钥
    ↓
在输入框中输入问题/查询
    ↓
点击发送按钮 (或按Enter)
    ↓
系统处理流程:
├─ 格式化选中卡片内容为结构化上下文
├─ 添加连线关系描述
├─ 组织成AI友好的格式
└─ 调用OpenRouter API
    ↓
显示"AI正在思考..."加载状态
    ↓
接收AI响应:
├─ 成功 → 显示AI回复内容
├─ 失败 → 显示错误信息和重试选项
└─ 超时 → 显示超时提示和重试按钮
    ↓
AI回复显示在对话面板中
    ↓
用户可以:
├─ 复制AI回复内容
├─ 基于回复创建新卡片
├─ 继续追问相关问题
└─ 导出对话记录
```

## Checklist Results Report

### Executive Summary
- **Overall PRD Completeness**: 90% - 文档结构完整，核心需求明确，用户流程详细
- **MVP Scope Appropriateness**: Just Right - 范围适中，4个史诗逻辑清晰
- **Readiness for Architecture Phase**: Ready - 技术要求明确，用户流程完整
- **Most Critical Gaps**: 无关键缺失，文档已包含所有必要信息

### Category Analysis Table

| Category                         | Status | Critical Issues |
| -------------------------------- | ------ | --------------- |
| 1. Problem Definition & Context  | PASS   | 无关键问题 |
| 2. MVP Scope Definition          | PASS   | 无关键问题 |
| 3. User Experience Requirements  | PASS   | 用户流程已补充完整 |
| 4. Functional Requirements       | PASS   | 无关键问题 |
| 5. Non-Functional Requirements   | PASS   | 无关键问题 |
| 6. Epic & Story Structure        | PASS   | 无关键问题 |
| 7. Technical Guidance            | PASS   | 技术栈明确 |
| 8. Cross-Functional Requirements | PASS   | 数据需求明确 |
| 9. Clarity & Communication       | PASS   | 无关键问题 |

### Final Decision
**READY FOR ARCHITECT**: PRD和史诗结构完整，核心需求明确，技术方向清晰，用户流程详细。文档已准备就绪，可以进入架构设计阶段。

## Next Steps

### UX Expert Prompt
"请基于这份PRD创建用户体验架构文档。重点关注：1) 画布交互的直观性设计，确保卡片创建、编辑、连线操作符合用户直觉；2) AI交互面板的信息架构，平衡功能丰富性与界面简洁性；3) 多画布切换的导航体验；4) Markdown编辑器的分屏布局优化。请使用UX架构模板开始设计。"

### Architect Prompt
"请基于这份PRD创建技术架构文档。核心技术挑战包括：1) SQLite WASM的性能优化和数据管理策略；2) Konva.js画布渲染与React组件的集成架构；3) 多画布的缓存和内存管理机制；4) OpenRouter API的错误处理和降级方案。请使用全栈架构模板，重点关注本地优先的技术实现方案。"
